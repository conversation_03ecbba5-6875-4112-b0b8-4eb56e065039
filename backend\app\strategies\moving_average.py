"""
Moving Average Crossover Strategy
"""

import pandas as pd
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
from app.core.models import OHLCVData
from app.strategies.base import BaseStrategy

class MovingAverageStrategy(BaseStrategy):
    """
    Moving Average Crossover Strategy
    
    Buy when fast MA crosses above slow MA
    Sell when fast MA crosses below slow MA
    """
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__(parameters)
        self.fast_period = parameters.get('fast_period', 10)
        self.slow_period = parameters.get('slow_period', 30)
        self.ma_type = parameters.get('ma_type', 'sma')
        
        # Validate parameters
        if self.fast_period >= self.slow_period:
            raise ValueError("Fast period must be less than slow period")
    
    def generate_signals(self, data: List[OHLCVData]) -> Tuple[pd.Series, Dict[str, List[Dict[str, Any]]]]:
        """
        Generate moving average crossover signals
        """
        df = self._convert_data_to_df(data)
        
        # Calculate moving averages
        if self.ma_type == 'ema':
            df['fast_ma'] = self._calculate_ema(df['close'], self.fast_period)
            df['slow_ma'] = self._calculate_ema(df['close'], self.slow_period)
        else:  # SMA
            df['fast_ma'] = self._calculate_sma(df['close'], self.fast_period)
            df['slow_ma'] = self._calculate_sma(df['close'], self.slow_period)
        
        # Generate signals
        df['signal'] = 0
        
        # Buy signal: fast MA crosses above slow MA
        df.loc[df['fast_ma'] > df['slow_ma'], 'signal'] = 1
        
        # Sell signal: fast MA crosses below slow MA
        df.loc[df['fast_ma'] < df['slow_ma'], 'signal'] = -1
        
        # Only generate signals on crossovers (not continuous)
        df['prev_signal'] = df['signal'].shift(1)
        df['crossover'] = df['signal'] != df['prev_signal']
        
        # Create final signal series (only on crossovers)
        signals = pd.Series(0, index=df.index)
        signals[df['crossover'] & (df['signal'] == 1)] = 1   # Buy on upward cross
        signals[df['crossover'] & (df['signal'] == -1)] = -1 # Sell on downward cross
        
        # Prepare indicator data for frontend
        indicators = {
            'fast_ma': self._format_indicator_data(df, 'fast_ma', f'Fast MA ({self.fast_period})'),
            'slow_ma': self._format_indicator_data(df, 'slow_ma', f'Slow MA ({self.slow_period})'),
            'signals': [
                {
                    'timestamp': timestamp.isoformat(),
                    'value': float(df.loc[timestamp, 'close']),
                    'type': 'buy' if signal == 1 else 'sell',
                    'name': 'Trade Signal'
                }
                for timestamp, signal in signals.items()
                if signal != 0
            ]
        }
        
        return signals, indicators
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for this strategy"""
        return {
            'fast_period': {
                'type': 'integer',
                'default': 10,
                'min': 1,
                'max': 200,
                'description': 'Fast moving average period'
            },
            'slow_period': {
                'type': 'integer',
                'default': 30,
                'min': 1,
                'max': 200,
                'description': 'Slow moving average period'
            },
            'ma_type': {
                'type': 'select',
                'default': 'sma',
                'options': ['sma', 'ema'],
                'description': 'Moving average type (Simple or Exponential)'
            }
        }
