import React from 'react';
import { useBacktestStore } from '../../store/backtestStore';
import { StrategyType } from '../../types';

export const StrategySelector: React.FC = () => {
  const { selectedStrategy, strategies, setSelectedStrategy } = useBacktestStore();

  const handleStrategyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const strategy = e.target.value as StrategyType;
    setSelectedStrategy(strategy);
  };

  // Default strategy options if strategies haven't loaded yet
  const defaultStrategies = [
    {
      name: 'moving_average',
      display_name: 'Moving Average Crossover',
      description: 'Buy when fast MA crosses above slow MA, sell when it crosses below'
    },
    {
      name: 'rsi',
      display_name: 'RSI Oscillator',
      description: 'Buy when RSI crosses below oversold, sell when it crosses above overbought'
    }
  ];

  const availableStrategies = strategies.length > 0 ? strategies : defaultStrategies;
  const currentStrategy = availableStrategies.find(s => s.name === selectedStrategy);

  return (
    <div className="space-y-3">
      <div>
        <label htmlFor="strategy-select" className="block text-sm font-medium text-gray-700 mb-2">
          Trading Strategy
        </label>
        <select
          id="strategy-select"
          value={selectedStrategy}
          onChange={handleStrategyChange}
          className="select w-full"
        >
          {availableStrategies.map((strategy) => (
            <option key={strategy.name} value={strategy.name}>
              {strategy.display_name}
            </option>
          ))}
        </select>
      </div>

      {/* Strategy Description */}
      {currentStrategy && (
        <div className="p-3 bg-gray-50 rounded-md">
          <p className="text-sm text-gray-600">
            {currentStrategy.description}
          </p>
        </div>
      )}
    </div>
  );
};
