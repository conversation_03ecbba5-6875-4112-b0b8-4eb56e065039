# Development Environment Configuration
ENVIRONMENT=development

# Database Configuration
POSTGRES_PASSWORD=quantdojo_dev_password
DATABASE_URL=***********************************************************/quantdojo

# Redis Configuration
REDIS_PASSWORD=redis_dev_password
REDIS_URL=redis://:redis_dev_password@redis:6379/0

# Application Security
SECRET_KEY=dev_secret_key_change_in_production_min_32_chars
JWT_SECRET_KEY=dev_jwt_secret_key_change_in_production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# External Services
SENTRY_DSN=

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development

# Build Configuration
BUILD_TARGET=development

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=json

# Rate Limiting (more lenient for development)
RATE_LIMIT_PER_MINUTE=120
RATE_LIMIT_BURST=20

# File Upload Limits
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=csv,xlsx,xls
