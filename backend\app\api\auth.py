"""
Authentication API endpoints for QuantDojo
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.auth import (
    AuthManager, TokenManager, PasswordManager,
    get_current_user, get_current_active_user
)
from app.core.errors import AuthenticationError, ValidationError
from app.core.logging import get_logger
from app.core.security import auth_rate_limit, InputValidator
from app.models.user import User, UserTier

router = APIRouter()
logger = get_logger("quantdojo.auth")


# Pydantic models
class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserResponse(BaseModel):
    id: int
    email: str
    username: str
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    tier: str
    bio: Optional[str]
    avatar_url: Optional[str]
    created_at: str
    updated_at: str
    last_login_at: Optional[str]


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class RefreshTokenRequest(BaseModel):
    refresh_token: str


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
@auth_rate_limit
async def register(request: Request, user_data: UserCreate, db: AsyncSession = Depends(get_db)):
    """Register a new user"""
    
    # Validate and sanitize input
    email = InputValidator.validate_email(user_data.email)
    username = InputValidator.validate_username(user_data.username)

    # Check if user already exists
    result = await db.execute(
        select(User).where(
            (User.email == email) | (User.username == username)
        )
    )
    existing_user = result.scalar_one_or_none()

    if existing_user:
        if existing_user.email == email:
            raise ValidationError("Email already registered", field="email")
        else:
            raise ValidationError("Username already taken", field="username")

    # Validate password strength
    if len(user_data.password) < 8:
        raise ValidationError("Password must be at least 8 characters long", field="password")
    
    # Create new user
    hashed_password = PasswordManager.hash_password(user_data.password)
    
    new_user = User(
        email=email,
        username=username,
        full_name=InputValidator.validate_string(user_data.full_name) if user_data.full_name else None,
        hashed_password=hashed_password,
        tier=UserTier.FREE,
        is_active=True,
        is_verified=False  # In production, require email verification
    )
    
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    
    logger.info("New user registered", user_id=new_user.id, email=new_user.email)
    
    return UserResponse(**new_user.to_dict())


@router.post("/login", response_model=TokenResponse)
@auth_rate_limit
async def login(request: Request, user_data: UserLogin, db: AsyncSession = Depends(get_db)):
    """Login user and return JWT tokens"""
    
    auth_manager = AuthManager(db)
    
    try:
        # Validate and sanitize input
        email = InputValidator.validate_email(user_data.email)

        # Authenticate user
        user = await auth_manager.authenticate_user(email, user_data.password)
        
        # Update last login time
        user.last_login_at = datetime.utcnow()
        await db.commit()
        
        # Create tokens
        token_data = {"sub": str(user.id), "email": user.email, "username": user.username}
        access_token = TokenManager.create_access_token(token_data)
        refresh_token = TokenManager.create_refresh_token(token_data)
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=30 * 60,  # 30 minutes in seconds
            user=UserResponse(**user.to_dict())
        )
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(token_data: RefreshTokenRequest, db: AsyncSession = Depends(get_db)):
    """Refresh access token using refresh token"""
    
    try:
        # Verify refresh token
        payload = TokenManager.verify_token(token_data.refresh_token, token_type="refresh")
        user_id = payload.get("sub")
        
        if user_id is None:
            raise AuthenticationError("Invalid token payload")
        
        # Get user from database
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if user is None or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new tokens
        token_data = {"sub": str(user.id), "email": user.email, "username": user.username}
        access_token = TokenManager.create_access_token(token_data)
        new_refresh_token = TokenManager.create_refresh_token(token_data)
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=30 * 60,  # 30 minutes in seconds
            user=UserResponse(**user.to_dict())
        )
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user information"""
    return UserResponse(**current_user.to_dict())


@router.post("/logout")
async def logout():
    """Logout user (client should discard tokens)"""
    # In a production system, you might want to maintain a token blacklist
    # For now, we rely on the client to discard the tokens
    return {"message": "Successfully logged out"}


# OAuth2 compatible endpoint for tools that expect it
@router.post("/token", response_model=TokenResponse)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """OAuth2 compatible login endpoint"""
    
    user_data = UserLogin(email=form_data.username, password=form_data.password)
    return await login(user_data, db)
