import React from 'react';
import { useBacktestStore } from '../../store/backtestStore';
import { TrendingUp, TrendingDown, Target, Award, DollarSign, BarChart3 } from 'lucide-react';

export const MetricsPanel: React.FC = () => {
  const { backtestResult } = useBacktestStore();

  if (!backtestResult) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm text-gray-600">No metrics available</p>
        <p className="text-xs text-gray-500">Run a backtest to see performance metrics</p>
      </div>
    );
  }

  const metrics = backtestResult.performance_metrics;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatNumber = (value: number, decimals: number = 2) => {
    return value.toFixed(decimals);
  };

  const getValueColor = (value: number, isPercentage: boolean = false) => {
    if (value > 0) return 'metric-positive';
    if (value < 0) return 'metric-negative';
    return 'metric-neutral';
  };

  return (
    <div className="space-y-4">
      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 gap-3">
        {/* Total Return */}
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <span className="metric-label">Total Return</span>
            </div>
            <div className="text-right">
              <div className={`metric-value text-lg ${getValueColor(metrics.total_return)}`}>
                {formatCurrency(metrics.total_return)}
              </div>
              <div className={`text-sm ${getValueColor(metrics.total_return_pct)}`}>
                {formatPercentage(metrics.total_return_pct)}
              </div>
            </div>
          </div>
        </div>

        {/* Sharpe Ratio */}
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-gray-500" />
              <span className="metric-label">Sharpe Ratio</span>
            </div>
            <div className={`metric-value text-lg ${getValueColor(metrics.sharpe_ratio)}`}>
              {formatNumber(metrics.sharpe_ratio)}
            </div>
          </div>
        </div>

        {/* Max Drawdown */}
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-4 w-4 text-gray-500" />
              <span className="metric-label">Max Drawdown</span>
            </div>
            <div className="text-right">
              <div className="metric-value text-lg metric-negative">
                {formatCurrency(metrics.max_drawdown)}
              </div>
              <div className="text-sm metric-negative">
                {formatPercentage(-metrics.max_drawdown_pct)}
              </div>
            </div>
          </div>
        </div>

        {/* Win Rate */}
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-gray-500" />
              <span className="metric-label">Win Rate</span>
            </div>
            <div className={`metric-value text-lg ${getValueColor(metrics.win_rate - 50)}`}>
              {formatPercentage(metrics.win_rate)}
            </div>
          </div>
        </div>

        {/* Profit Factor */}
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-gray-500" />
              <span className="metric-label">Profit Factor</span>
            </div>
            <div className={`metric-value text-lg ${getValueColor(metrics.profit_factor - 1)}`}>
              {formatNumber(metrics.profit_factor)}
            </div>
          </div>
        </div>
      </div>

      {/* Trade Statistics */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Trade Statistics</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Total Trades:</span>
            <span className="font-medium">{metrics.total_trades}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Winning Trades:</span>
            <span className="font-medium text-success-600">{metrics.winning_trades}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Losing Trades:</span>
            <span className="font-medium text-danger-600">{metrics.losing_trades}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Avg Trade:</span>
            <span className={`font-medium ${getValueColor(metrics.avg_trade)}`}>
              {formatCurrency(metrics.avg_trade)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Avg Win:</span>
            <span className="font-medium text-success-600">
              {formatCurrency(metrics.avg_win)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Avg Loss:</span>
            <span className="font-medium text-danger-600">
              {formatCurrency(metrics.avg_loss)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Largest Win:</span>
            <span className="font-medium text-success-600">
              {formatCurrency(metrics.largest_win)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Largest Loss:</span>
            <span className="font-medium text-danger-600">
              {formatCurrency(metrics.largest_loss)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
