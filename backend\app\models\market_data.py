"""
Market data model for QuantDojo
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Float, Index
from sqlalchemy.sql import func

from app.models.base import Base


class MarketData(Base):
    """Market data model for storing OHLCV data"""
    __tablename__ = "market_data"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # OHLCV data
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)
    
    # Adjusted close for stock splits/dividends
    adj_close = Column(Float, nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Composite index for efficient queries
    __table_args__ = (
        Index('ix_market_data_symbol_timestamp', 'symbol', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<MarketData(symbol='{self.symbol}', timestamp='{self.timestamp}', close={self.close})>"
