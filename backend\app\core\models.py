"""
Pydantic Models for QuantDojo API
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class StrategyType(str, Enum):
    MOVING_AVERAGE = "moving_average"
    RSI = "rsi"

class MAType(str, Enum):
    SMA = "sma"
    EMA = "ema"

class OHLCVData(BaseModel):
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int

class MarketDataRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol (e.g., AAPL)")
    period: str = Field(default="1y", description="Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)")
    interval: str = Field(default="1d", description="Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)")

class MarketDataResponse(BaseModel):
    symbol: str
    data: List[OHLCVData]
    metadata: Dict[str, Any]

class MovingAverageParams(BaseModel):
    fast_period: int = Field(default=10, ge=1, le=200, description="Fast moving average period")
    slow_period: int = Field(default=30, ge=1, le=200, description="Slow moving average period")
    ma_type: MAType = Field(default=MAType.SMA, description="Moving average type")

class RSIParams(BaseModel):
    period: int = Field(default=14, ge=2, le=100, description="RSI calculation period")
    overbought: float = Field(default=70.0, ge=50.0, le=100.0, description="Overbought threshold")
    oversold: float = Field(default=30.0, ge=0.0, le=50.0, description="Oversold threshold")

class BacktestRequest(BaseModel):
    symbol: str = Field(..., description="Stock symbol")
    strategy: StrategyType = Field(..., description="Strategy type")
    parameters: Union[MovingAverageParams, RSIParams] = Field(..., description="Strategy parameters")
    start_date: Optional[str] = Field(None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")
    initial_capital: float = Field(default=10000.0, ge=1000.0, description="Initial capital")
    commission: float = Field(default=0.001, ge=0.0, le=0.1, description="Commission rate")
    slippage: float = Field(default=0.001, ge=0.0, le=0.1, description="Slippage rate")

class Trade(BaseModel):
    entry_date: datetime
    exit_date: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    quantity: int
    side: str  # "buy" or "sell"
    pnl: Optional[float]
    pnl_pct: Optional[float]

class PerformanceMetrics(BaseModel):
    total_return: float
    total_return_pct: float
    sharpe_ratio: float
    max_drawdown: float
    max_drawdown_pct: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_trade: float
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float

class BacktestResult(BaseModel):
    symbol: str
    strategy: str
    parameters: Dict[str, Any]
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    performance_metrics: PerformanceMetrics
    trades: List[Trade]
    equity_curve: List[Dict[str, Union[datetime, float]]]
    indicators: Dict[str, List[Dict[str, Union[datetime, float]]]]

class StrategyInfo(BaseModel):
    name: str
    display_name: str
    description: str
    parameters: Dict[str, Any]
    indicators: List[str]

class StrategyListResponse(BaseModel):
    strategies: List[StrategyInfo]
