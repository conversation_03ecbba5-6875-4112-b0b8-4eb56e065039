"""
Authentication and authorization utilities for QuantDojo
Provides JWT token management and password hashing
"""

import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.errors import AuthenticationError, AuthorizationError
from app.core.logging import get_logger

logger = get_logger("quantdojo.auth")

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "dev_secret_key_change_in_production")
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "dev_jwt_secret_key_change_in_production")
ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_EXPIRE_MINUTES", "30"))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_EXPIRE_DAYS", "7"))

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


class PasswordManager:
    """Password hashing and verification utilities"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)


class TokenManager:
    """JWT token management utilities"""
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create an access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """Create a refresh token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode a token"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
            
            # Check token type
            if payload.get("type") != token_type:
                raise AuthenticationError("Invalid token type")
            
            # Check expiration
            exp = payload.get("exp")
            if exp is None or datetime.utcnow() > datetime.fromtimestamp(exp):
                raise AuthenticationError("Token has expired")
            
            return payload
            
        except JWTError as e:
            logger.warning("JWT verification failed", error=str(e))
            raise AuthenticationError("Invalid token")


class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def authenticate_user(self, email: str, password: str):
        """Authenticate a user with email and password"""
        from app.models.user import User
        from sqlalchemy import select
        
        # Get user by email
        result = await self.db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()
        
        if not user:
            logger.warning("Authentication failed - user not found", email=email)
            raise AuthenticationError("Invalid credentials")
        
        if not user.is_active:
            logger.warning("Authentication failed - user inactive", email=email)
            raise AuthenticationError("Account is inactive")
        
        if not PasswordManager.verify_password(password, user.hashed_password):
            logger.warning("Authentication failed - invalid password", email=email)
            raise AuthenticationError("Invalid credentials")
        
        logger.info("User authenticated successfully", user_id=user.id, email=email)
        return user
    
    async def get_current_user(self, token: str):
        """Get current user from token"""
        from app.models.user import User
        from sqlalchemy import select
        
        # Verify token
        payload = TokenManager.verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise AuthenticationError("Invalid token payload")
        
        # Get user from database
        result = await self.db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if user is None:
            raise AuthenticationError("User not found")
        
        if not user.is_active:
            raise AuthenticationError("Account is inactive")
        
        return user
    
    async def check_user_permission(self, user, required_tier: str = "free"):
        """Check if user has required permission tier"""
        tier_hierarchy = {"free": 0, "pro": 1, "enterprise": 2}
        
        user_tier_level = tier_hierarchy.get(user.tier, 0)
        required_tier_level = tier_hierarchy.get(required_tier, 0)
        
        if user_tier_level < required_tier_level:
            raise AuthorizationError(
                f"Access denied. Required tier: {required_tier}, current tier: {user.tier}"
            )
        
        return True


# Dependency functions
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """FastAPI dependency to get current authenticated user"""
    auth_manager = AuthManager(db)
    return await auth_manager.get_current_user(credentials.credentials)


async def get_current_active_user(current_user = Depends(get_current_user)):
    """FastAPI dependency to get current active user"""
    if not current_user.is_active:
        raise AuthenticationError("Account is inactive")
    return current_user


def require_tier(required_tier: str):
    """Decorator to require specific user tier"""
    async def tier_dependency(
        current_user = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_db)
    ):
        auth_manager = AuthManager(db)
        await auth_manager.check_user_permission(current_user, required_tier)
        return current_user
    
    return tier_dependency


# Optional authentication (for endpoints that work with or without auth)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
):
    """FastAPI dependency to optionally get current user"""
    if credentials is None:
        return None
    
    try:
        auth_manager = AuthManager(db)
        return await auth_manager.get_current_user(credentials.credentials)
    except AuthenticationError:
        return None
