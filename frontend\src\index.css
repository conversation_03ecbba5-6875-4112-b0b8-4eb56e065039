@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .select {
    @apply input cursor-pointer;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-600;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .metric-card {
    @apply card p-4;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-600;
  }
  
  .metric-value {
    @apply text-2xl font-bold;
  }
  
  .metric-positive {
    @apply text-success-600;
  }
  
  .metric-negative {
    @apply text-danger-600;
  }
  
  .metric-neutral {
    @apply text-gray-900;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Chart container styles */
.chart-container {
  @apply relative w-full bg-white rounded-lg border border-gray-200;
}

.chart-loading {
  @apply absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10;
}

/* Slider styles */
.slider {
  @apply w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer;
}

.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-primary-600 rounded-full cursor-pointer;
}

.slider::-moz-range-thumb {
  @apply w-4 h-4 bg-primary-600 rounded-full cursor-pointer border-0;
}
