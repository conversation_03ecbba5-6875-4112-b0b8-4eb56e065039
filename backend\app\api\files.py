"""
File upload and management API endpoints for QuantDojo
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.core.security import FileValidator, default_rate_limit
from app.core.errors import ValidationError
from app.core.logging import get_logger
from app.models.user import User

router = APIRouter()
logger = get_logger("quantdojo.files")

# Upload directory
UPLOAD_DIR = Path(os.getenv("UPLOAD_DIR", "uploads"))
UPLOAD_DIR.mkdir(exist_ok=True)


# Pydantic models
class FileInfo(BaseModel):
    filename: str
    original_filename: str
    size: int
    content_type: str
    upload_time: str


class FileUploadResponse(BaseModel):
    success: bool
    message: str
    file_info: Optional[FileInfo] = None


@router.post("/upload", response_model=FileUploadResponse)
@default_rate_limit
async def upload_file(
    request: Request,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Upload a file with security validation"""
    
    try:
        # Validate file
        FileValidator.validate_file_size(file)
        FileValidator.validate_file_extension(file.filename)
        await FileValidator.validate_file_content(file)
        
        # Generate safe filename
        safe_filename = FileValidator.generate_safe_filename(file.filename)
        
        # Create user-specific directory
        user_dir = UPLOAD_DIR / str(current_user.id)
        user_dir.mkdir(exist_ok=True)
        
        # Save file
        file_path = user_dir / safe_filename
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Scan for malware
        if not await FileValidator.scan_file_for_malware(str(file_path)):
            # Remove the file if it's suspicious
            file_path.unlink()
            raise ValidationError("File failed security scan")
        
        logger.info(
            "File uploaded successfully",
            user_id=current_user.id,
            filename=safe_filename,
            original_filename=file.filename,
            size=file.size
        )
        
        return FileUploadResponse(
            success=True,
            message="File uploaded successfully",
            file_info=FileInfo(
                filename=safe_filename,
                original_filename=file.filename,
                size=file.size or 0,
                content_type=file.content_type or "unknown",
                upload_time=str(file_path.stat().st_mtime)
            )
        )
        
    except ValidationError as e:
        logger.warning(
            "File upload validation failed",
            user_id=current_user.id,
            filename=file.filename,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(
            "File upload failed",
            user_id=current_user.id,
            filename=file.filename,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.get("/list", response_model=List[FileInfo])
@default_rate_limit
async def list_files(
    request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """List user's uploaded files"""
    
    user_dir = UPLOAD_DIR / str(current_user.id)
    
    if not user_dir.exists():
        return []
    
    files = []
    for file_path in user_dir.iterdir():
        if file_path.is_file():
            stat = file_path.stat()
            files.append(FileInfo(
                filename=file_path.name,
                original_filename=file_path.name,  # We don't store original names currently
                size=stat.st_size,
                content_type="unknown",  # We don't store content types currently
                upload_time=str(stat.st_mtime)
            ))
    
    return files


@router.delete("/delete/{filename}")
@default_rate_limit
async def delete_file(
    request: Request,
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a user's uploaded file"""
    
    # Validate filename to prevent path traversal
    if ".." in filename or "/" in filename or "\\" in filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid filename"
        )
    
    user_dir = UPLOAD_DIR / str(current_user.id)
    file_path = user_dir / filename
    
    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    try:
        file_path.unlink()
        logger.info(
            "File deleted successfully",
            user_id=current_user.id,
            filename=filename
        )
        return {"success": True, "message": "File deleted successfully"}
    
    except Exception as e:
        logger.error(
            "File deletion failed",
            user_id=current_user.id,
            filename=filename,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File deletion failed"
        )


@router.get("/download/{filename}")
@default_rate_limit
async def download_file(
    request: Request,
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """Download a user's uploaded file"""
    
    # Validate filename to prevent path traversal
    if ".." in filename or "/" in filename or "\\" in filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid filename"
        )
    
    user_dir = UPLOAD_DIR / str(current_user.id)
    file_path = user_dir / filename
    
    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    from fastapi.responses import FileResponse
    
    return FileResponse(
        path=str(file_path),
        filename=filename,
        media_type='application/octet-stream'
    )
