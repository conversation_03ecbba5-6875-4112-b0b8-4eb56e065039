"""
Error handling utilities for QuantDojo
Provides custom exceptions and error tracking
"""

from typing import Any, Dict, Optional
import traceback

import sentry_sdk
from fastapi import HTTPException, status

from app.core.logging import get_logger

logger = get_logger("quantdojo.errors")


class QuantDojoException(Exception):
    """Base exception for QuantDojo application"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "QUANTDOJO_ERROR",
        details: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(self.message)


class ValidationError(QuantDojoException):
    """Validation error exception"""
    
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"field": field, **(details or {})},
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )


class DataProviderError(QuantDojoException):
    """Data provider error exception"""
    
    def __init__(self, message: str, provider: str, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="DATA_PROVIDER_ERROR",
            details={"provider": provider, **(details or {})},
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE
        )


class BacktestError(QuantDojoException):
    """Backtest execution error exception"""
    
    def __init__(self, message: str, strategy: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="BACKTEST_ERROR",
            details={"strategy": strategy, **(details or {})},
            status_code=status.HTTP_400_BAD_REQUEST
        )


class AuthenticationError(QuantDojoException):
    """Authentication error exception"""
    
    def __init__(self, message: str = "Authentication failed", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationError(QuantDojoException):
    """Authorization error exception"""
    
    def __init__(self, message: str = "Access denied", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details,
            status_code=status.HTTP_403_FORBIDDEN
        )


class RateLimitError(QuantDojoException):
    """Rate limit exceeded exception"""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )


def handle_exception(exc: Exception, context: Dict[str, Any] = None) -> HTTPException:
    """
    Handle exceptions and convert them to HTTP exceptions
    Also logs the error and sends to Sentry if configured
    """
    context = context or {}
    
    # Log the error
    logger.error(
        "Exception occurred",
        error=str(exc),
        error_type=type(exc).__name__,
        traceback=traceback.format_exc(),
        **context
    )
    
    # Send to Sentry
    with sentry_sdk.push_scope() as scope:
        for key, value in context.items():
            scope.set_extra(key, value)
        sentry_sdk.capture_exception(exc)
    
    # Convert to HTTP exception
    if isinstance(exc, QuantDojoException):
        return HTTPException(
            status_code=exc.status_code,
            detail={
                "error": exc.error_code,
                "message": exc.message,
                "details": exc.details
            }
        )
    elif isinstance(exc, HTTPException):
        return exc
    else:
        # Generic server error
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_SERVER_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )


def log_and_raise(
    exception_class: type,
    message: str,
    context: Dict[str, Any] = None,
    **kwargs
) -> None:
    """
    Log an error and raise an exception
    """
    context = context or {}
    
    logger.error(message, **context)
    
    # Send to Sentry
    with sentry_sdk.push_scope() as scope:
        for key, value in context.items():
            scope.set_extra(key, value)
        sentry_sdk.capture_message(message, level="error")
    
    raise exception_class(message, **kwargs)
