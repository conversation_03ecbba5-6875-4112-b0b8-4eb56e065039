"""
Health check endpoints for QuantDojo API
Provides comprehensive health monitoring including database and external services
"""

import asyncio
import os
import time
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
import redis.asyncio as redis
import httpx

from app.core.logging import get_logger
from app.core.database import get_async_session
from app.core.monitoring import metrics, HealthChecker

router = APIRouter()
logger = get_logger("quantdojo.health")


@router.get("/health")
async def basic_health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "timestamp": time.time()
    }


@router.get("/health/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_async_session)):
    """Detailed health check with dependency verification"""
    health_status = {
        "status": "healthy",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "timestamp": time.time(),
        "checks": {}
    }
    
    # Check database connectivity
    health_status["checks"]["database"] = await HealthChecker.check_database(db)
    if health_status["checks"]["database"]["status"] == "unhealthy":
        health_status["status"] = "degraded"

    # Check Redis connectivity
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    health_status["checks"]["redis"] = await HealthChecker.check_redis(redis_url)
    if health_status["checks"]["redis"]["status"] == "unhealthy":
        health_status["status"] = "degraded"

    # Check external data provider (yfinance via Yahoo Finance)
    health_status["checks"]["external_data"] = await HealthChecker.check_external_service(
        "https://finance.yahoo.com"
    )
    health_status["checks"]["external_data"]["provider"] = "yahoo_finance"
    
    # Overall status determination
    unhealthy_checks = [
        check for check in health_status["checks"].values()
        if check["status"] == "unhealthy"
    ]
    
    if unhealthy_checks:
        health_status["status"] = "unhealthy"
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_async_session)):
    """Kubernetes-style readiness probe"""
    try:
        # Check if database is ready
        db_check = await HealthChecker.check_database(db)
        if db_check["status"] != "healthy":
            raise Exception(f"Database not ready: {db_check.get('error', 'Unknown error')}")

        # Check if Redis is ready
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        redis_check = await HealthChecker.check_redis(redis_url)
        if redis_check["status"] != "healthy":
            raise Exception(f"Redis not ready: {redis_check.get('error', 'Unknown error')}")

        return {"status": "ready"}

    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={"status": "not_ready", "error": str(e)}
        )


@router.get("/live")
async def liveness_check():
    """Kubernetes-style liveness probe"""
    return {"status": "alive"}


@router.get("/metrics")
async def metrics_endpoint():
    """Application metrics endpoint for monitoring"""
    return metrics.get_metrics()
