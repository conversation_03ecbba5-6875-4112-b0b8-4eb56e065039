import axios from 'axios';
import { 
  MarketDataResponse, 
  BacktestRequest, 
  BacktestResult, 
  StrategyInfo 
} from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.detail || error.response.data?.message || 'Server error';
      throw new Error(`${error.response.status}: ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error: Unable to connect to server');
    } else {
      // Something else happened
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
);

// Market data API
export const fetchMarketData = async (
  symbol: string, 
  period: string = '1y',
  interval: string = '1d'
): Promise<MarketDataResponse> => {
  const response = await api.get(`/data/${symbol}`, {
    params: { period, interval }
  });
  return response.data;
};

export const uploadCSVData = async (file: File): Promise<MarketDataResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await api.post('/data/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const searchSymbols = async (query: string): Promise<any> => {
  const response = await api.get(`/data/search/${query}`);
  return response.data;
};

// Backtesting API
export const runBacktest = async (request: BacktestRequest): Promise<BacktestResult> => {
  const response = await api.post('/backtest', request);
  return response.data;
};

export const validateStrategyParameters = async (
  strategyName: string, 
  parameters: Record<string, any>
): Promise<any> => {
  const response = await api.get(`/backtest/validate-parameters/${strategyName}`, {
    params: parameters
  });
  return response.data;
};

// Strategies API
export const fetchStrategies = async (): Promise<StrategyInfo[]> => {
  const response = await api.get('/strategies');
  return response.data.strategies;
};

export const fetchStrategyInfo = async (strategyName: string): Promise<StrategyInfo> => {
  const response = await api.get(`/strategies/${strategyName}`);
  return response.data;
};

export const fetchStrategyParameters = async (strategyName: string): Promise<Record<string, any>> => {
  const response = await api.get(`/strategies/${strategyName}/parameters`);
  return response.data;
};

// Health check
export const healthCheck = async (): Promise<{ status: string }> => {
  const response = await api.get('/health');
  return response.data;
};

export default api;
