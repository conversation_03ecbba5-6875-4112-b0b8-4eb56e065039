"""
Structured logging configuration for QuantDojo
Provides JSON-formatted logging with Sentry integration
"""

import logging
import logging.config
import os
import sys
import time
from typing import Any, Dict

import sentry_sdk
import structlog
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration


def configure_sentry() -> None:
    """Configure Sentry for error tracking"""
    sentry_dsn = os.getenv("SENTRY_DSN")
    environment = os.getenv("ENVIRONMENT", "development")
    
    if sentry_dsn:
        sentry_logging = LoggingIntegration(
            level=logging.INFO,        # Capture info and above as breadcrumbs
            event_level=logging.ERROR  # Send errors as events
        )
        
        sentry_sdk.init(
            dsn=sentry_dsn,
            environment=environment,
            integrations=[
                FastApiIntegration(auto_enable=True),
                SqlalchemyIntegration(),
                sentry_logging,
            ],
            traces_sample_rate=0.1 if environment == "production" else 1.0,
            send_default_pii=False,
            attach_stacktrace=True,
            before_send=filter_sensitive_data,
        )


def filter_sensitive_data(event: Dict[str, Any], hint: Dict[str, Any]) -> Dict[str, Any]:
    """Filter sensitive data from Sentry events"""
    # Remove sensitive keys from event data
    sensitive_keys = {
        'password', 'token', 'secret', 'key', 'authorization',
        'cookie', 'session', 'csrf', 'api_key'
    }
    
    def clean_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        if not isinstance(data, dict):
            return data
            
        cleaned = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                cleaned[key] = "[FILTERED]"
            elif isinstance(value, dict):
                cleaned[key] = clean_dict(value)
            elif isinstance(value, list):
                cleaned[key] = [clean_dict(item) if isinstance(item, dict) else item for item in value]
            else:
                cleaned[key] = value
        return cleaned
    
    # Clean request data
    if 'request' in event:
        event['request'] = clean_dict(event['request'])
    
    # Clean extra data
    if 'extra' in event:
        event['extra'] = clean_dict(event['extra'])
    
    return event


def configure_structlog() -> None:
    """Configure structured logging with JSON output"""
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    log_format = os.getenv("LOG_FORMAT", "json").lower()
    environment = os.getenv("ENVIRONMENT", "development")
    
    # Configure timestamper
    timestamper = structlog.processors.TimeStamper(fmt="ISO")
    
    # Configure processors based on environment
    if environment == "development" and log_format != "json":
        # Pretty console output for development
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            timestamper,
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.dev.ConsoleRenderer(colors=True)
        ]
    else:
        # JSON output for production
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            timestamper,
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ]
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level),
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a configured logger instance"""
    return structlog.get_logger(name)


class LoggingMiddleware:
    """FastAPI middleware for request/response logging and metrics"""

    def __init__(self, app):
        self.app = app
        self.logger = get_logger("quantdojo.middleware")

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # Import metrics here to avoid circular imports
        from app.core.monitoring import metrics

        # Increment request counter
        metrics.increment_requests()

        # Extract request info
        request_info = {
            "method": scope["method"],
            "path": scope["path"],
            "query_string": scope.get("query_string", b"").decode(),
            "client": scope.get("client"),
            "user_agent": None,
        }

        # Extract user agent from headers
        for name, value in scope.get("headers", []):
            if name == b"user-agent":
                request_info["user_agent"] = value.decode()
                break

        # Log request
        self.logger.info("Request started", **request_info)

        # Process request and capture response
        response_info = {"status_code": None}
        start_time = time.time()

        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                response_info["status_code"] = message["status"]
            await send(message)

        try:
            await self.app(scope, receive, send_wrapper)

            # Calculate response time
            response_time = (time.time() - start_time) * 1000

            # Log successful response
            self.logger.info(
                "Request completed",
                status_code=response_info["status_code"],
                response_time_ms=round(response_time, 2),
                **request_info
            )

        except Exception as exc:
            # Increment error counter
            metrics.increment_errors()

            # Calculate response time
            response_time = (time.time() - start_time) * 1000

            # Log error
            self.logger.error(
                "Request failed",
                error=str(exc),
                error_type=type(exc).__name__,
                response_time_ms=round(response_time, 2),
                **request_info
            )
            raise


# Initialize logging on module import
configure_sentry()
configure_structlog()
