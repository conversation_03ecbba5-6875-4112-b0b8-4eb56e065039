"""
Data API endpoints for fetching market data
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import Optional
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import io

from app.core.models import MarketDataRequest, MarketDataResponse, OHLCVData
from app.core.config import settings

router = APIRouter()

@router.get("/{symbol}")
async def get_market_data(
    symbol: str,
    period: str = "1y",
    interval: str = "1d"
) -> MarketDataResponse:
    """
    Fetch OHLCV data for a given symbol using yfinance
    """
    try:
        # Create ticker object
        ticker = yf.Ticker(symbol.upper())
        
        # Fetch historical data
        hist = ticker.history(period=period, interval=interval)
        
        if hist.empty:
            raise HTTPException(
                status_code=404,
                detail=f"No data found for symbol {symbol}"
            )
        
        # Convert to our format
        ohlcv_data = []
        for timestamp, row in hist.iterrows():
            ohlcv_data.append(OHLCVData(
                timestamp=timestamp.to_pydatetime(),
                open=float(row['Open']),
                high=float(row['High']),
                low=float(row['Low']),
                close=float(row['Close']),
                volume=int(row['Volume'])
            ))
        
        # Get ticker info for metadata
        info = ticker.info
        metadata = {
            "symbol": symbol.upper(),
            "company_name": info.get("longName", "Unknown"),
            "sector": info.get("sector", "Unknown"),
            "industry": info.get("industry", "Unknown"),
            "currency": info.get("currency", "USD"),
            "exchange": info.get("exchange", "Unknown"),
            "period": period,
            "interval": interval,
            "data_points": len(ohlcv_data)
        }
        
        return MarketDataResponse(
            symbol=symbol.upper(),
            data=ohlcv_data,
            metadata=metadata
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching data for {symbol}: {str(e)}"
        )

@router.post("/upload")
async def upload_csv_data(file: UploadFile = File(...)):
    """
    Upload custom CSV data file
    Expected format: Date,Open,High,Low,Close,Volume
    """
    try:
        # Read the uploaded file
        contents = await file.read()
        
        # Parse CSV
        df = pd.read_csv(io.StringIO(contents.decode('utf-8')))
        
        # Validate required columns
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required columns: {missing_columns}"
            )
        
        # Convert date column
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.sort_values('Date')
        
        # Convert to our format
        ohlcv_data = []
        for _, row in df.iterrows():
            ohlcv_data.append(OHLCVData(
                timestamp=row['Date'].to_pydatetime(),
                open=float(row['Open']),
                high=float(row['High']),
                low=float(row['Low']),
                close=float(row['Close']),
                volume=int(row['Volume'])
            ))
        
        metadata = {
            "symbol": "CUSTOM",
            "company_name": "Custom Data",
            "source": "CSV Upload",
            "filename": file.filename,
            "data_points": len(ohlcv_data),
            "date_range": {
                "start": df['Date'].min().isoformat(),
                "end": df['Date'].max().isoformat()
            }
        }
        
        return MarketDataResponse(
            symbol="CUSTOM",
            data=ohlcv_data,
            metadata=metadata
        )
        
    except pd.errors.EmptyDataError:
        raise HTTPException(status_code=400, detail="Empty CSV file")
    except pd.errors.ParserError as e:
        raise HTTPException(status_code=400, detail=f"CSV parsing error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@router.get("/search/{query}")
async def search_symbols(query: str):
    """
    Search for stock symbols (basic implementation)
    """
    try:
        # This is a simple implementation - in production you might want to use
        # a proper symbol search API
        ticker = yf.Ticker(query.upper())
        info = ticker.info
        
        if not info or 'symbol' not in info:
            return {"results": []}
        
        return {
            "results": [{
                "symbol": info.get("symbol", query.upper()),
                "name": info.get("longName", "Unknown"),
                "exchange": info.get("exchange", "Unknown"),
                "currency": info.get("currency", "USD")
            }]
        }
        
    except Exception:
        return {"results": []}
