import { create } from 'zustand';
import { BacktestState, StrategyType, StrategyParams, MovingAverageParams, RSIParams } from '../types';
import { fetchMarketData, runBacktest, fetchStrategies } from '../utils/api';

const defaultMovingAverageParams: MovingAverageParams = {
  fast_period: 10,
  slow_period: 30,
  ma_type: 'sma'
};

const defaultRSIParams: RSIParams = {
  period: 14,
  overbought: 70,
  oversold: 30
};

export const useBacktestStore = create<BacktestState>((set, get) => ({
  // Data
  marketData: null,
  backtestResult: null,
  strategies: [],

  // UI state
  isLoading: false,
  error: null,

  // Current configuration
  symbol: 'AAPL',
  selectedStrategy: 'moving_average',
  strategyParams: defaultMovingAverageParams,
  backtestConfig: {
    initial_capital: 10000,
    commission: 0.001,
    slippage: 0.001,
  },

  // Actions
  setSymbol: (symbol: string) => {
    set({ symbol: symbol.toUpperCase() });
  },

  setSelectedStrategy: (strategy: StrategyType) => {
    const newParams: StrategyParams = strategy === 'moving_average'
      ? defaultMovingAverageParams
      : defaultRSIParams;

    set({
      selectedStrategy: strategy,
      strategyParams: newParams,
      backtestResult: null // Clear previous results
    });
  },

  setStrategyParams: (params: StrategyParams) => {
    set({
      strategyParams: params,
      backtestResult: null // Clear previous results when params change
    });
  },

  setBacktestConfig: (config) => {
    set(state => ({
      backtestConfig: { ...state.backtestConfig, ...config },
      backtestResult: null // Clear previous results when config changes
    }));
  },

  fetchMarketData: async (symbol: string, period = '1y') => {
    set({ isLoading: true, error: null });

    try {
      const data = await fetchMarketData(symbol, period);
      set({
        marketData: data,
        symbol: symbol.toUpperCase(),
        isLoading: false
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch market data',
        isLoading: false
      });
    }
  },

  runBacktest: async () => {
    const state = get();

    if (!state.marketData) {
      set({ error: 'No market data available. Please fetch data first.' });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const request = {
        symbol: state.symbol,
        strategy: state.selectedStrategy,
        parameters: state.strategyParams,
        initial_capital: state.backtestConfig.initial_capital,
        commission: state.backtestConfig.commission,
        slippage: state.backtestConfig.slippage,
        start_date: state.backtestConfig.start_date,
        end_date: state.backtestConfig.end_date,
      };

      const result = await runBacktest(request);
      set({
        backtestResult: result,
        isLoading: false
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Backtest failed',
        isLoading: false
      });
    }
  },

  fetchStrategies: async () => {
    try {
      const strategies = await fetchStrategies();
      set({ strategies });
    } catch (error) {
      console.error('Failed to fetch strategies:', error);
    }
  },

  setError: (error: string | null) => {
    set({ error });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
