import React, { useEffect, useRef } from 'react';
import { createChart, IChartApi, ISeriesApi, CandlestickData, LineData, Time } from 'lightweight-charts';
import { useBacktestStore } from '../../store/backtestStore';
import { ChartData, LineData as CustomLineData, MarkerData } from '../../types';

export const TradingChart: React.FC = () => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const indicatorSeriesRef = useRef<{ [key: string]: ISeriesApi<'Line'> }>({});

  const { marketData, backtestResult, isLoading } = useBacktestStore();

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#22c55e',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#22c55e',
      wickDownColor: '#ef4444',
      wickUpColor: '#22c55e',
    });

    // Create volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: 'volume',
    });

    chart.priceScale('volume').applyOptions({
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    volumeSeriesRef.current = volumeSeries;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
      }
    };
  }, []);

  // Update chart data
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !volumeSeriesRef.current) return;

    if (marketData && marketData.data.length > 0) {
      // Prepare candlestick data
      const candlestickData: CandlestickData[] = marketData.data.map(item => ({
        time: (new Date(item.timestamp).getTime() / 1000) as Time,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }));

      // Prepare volume data
      const volumeData = marketData.data.map(item => ({
        time: (new Date(item.timestamp).getTime() / 1000) as Time,
        value: item.volume,
        color: item.close >= item.open ? '#22c55e' : '#ef4444',
      }));

      candlestickSeriesRef.current.setData(candlestickData);
      volumeSeriesRef.current.setData(volumeData);

      // Fit content to show all data
      chartRef.current.timeScale().fitContent();
    }
  }, [marketData]);

  // Update indicators and signals
  useEffect(() => {
    if (!chartRef.current || !backtestResult) return;

    // Clear existing indicator series
    Object.values(indicatorSeriesRef.current).forEach(series => {
      chartRef.current?.removeSeries(series);
    });
    indicatorSeriesRef.current = {};

    // Add indicator lines
    if (backtestResult.indicators) {
      Object.entries(backtestResult.indicators).forEach(([key, data]) => {
        if (key === 'signals') return; // Handle signals separately

        // Check if data is indicator data (has value property)
        const indicatorData = data as any[];
        if (indicatorData.length > 0 && 'value' in indicatorData[0]) {
          const lineData: LineData[] = indicatorData
            .filter(point => point.value !== null)
            .map(point => ({
              time: (new Date(point.timestamp).getTime() / 1000) as Time,
              value: point.value,
            }));

          if (lineData.length > 0) {
            const color = getIndicatorColor(key);
            const lineSeries = chartRef.current!.addLineSeries({
              color,
              lineWidth: 2,
              title: indicatorData[0]?.name || key,
            });

            lineSeries.setData(lineData);
            indicatorSeriesRef.current[key] = lineSeries;
          }
        }
      });

      // Add trade signals as markers
      if (backtestResult.indicators.signals) {
        const signals = backtestResult.indicators.signals as any[];
        const markers = signals.map(signal => ({
          time: (new Date(signal.timestamp).getTime() / 1000) as Time,
          position: signal.type === 'buy' ? 'belowBar' as const : 'aboveBar' as const,
          color: signal.type === 'buy' ? '#22c55e' : '#ef4444',
          shape: signal.type === 'buy' ? 'arrowUp' as const : 'arrowDown' as const,
          text: signal.type === 'buy' ? 'BUY' : 'SELL',
          size: 1,
        }));

        if (candlestickSeriesRef.current) {
          candlestickSeriesRef.current.setMarkers(markers);
        }
      }
    }
  }, [backtestResult]);

  const getIndicatorColor = (key: string): string => {
    const colors: { [key: string]: string } = {
      fast_ma: '#3b82f6',
      slow_ma: '#f59e0b',
      rsi: '#8b5cf6',
      overbought_line: '#ef4444',
      oversold_line: '#22c55e',
    };
    return colors[key] || '#6b7280';
  };

  if (isLoading) {
    return (
      <div className="chart-container">
        <div className="chart-loading">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading chart...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!marketData) {
    return (
      <div className="chart-container">
        <div className="chart-loading">
          <div className="text-center">
            <p className="text-gray-600">No data to display</p>
            <p className="text-sm text-gray-500 mt-1">Enter a symbol to load market data</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="chart-container">
      <div ref={chartContainerRef} className="w-full h-full" />
      
      {/* Chart Legend */}
      {backtestResult && backtestResult.indicators && (
        <div className="absolute top-2 left-2 bg-white bg-opacity-90 rounded p-2 text-xs space-y-1">
          {Object.entries(backtestResult.indicators).map(([key, data]) => {
            if (key === 'signals') return null;
            const indicatorData = data as any[];
            const name = indicatorData[0]?.name || key;
            const color = getIndicatorColor(key);
            
            return (
              <div key={key} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-0.5" 
                  style={{ backgroundColor: color }}
                />
                <span className="text-gray-700">{name}</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
