"""
Database performance monitoring and optimization for QuantDojo
Provides query logging, performance metrics, and connection pool monitoring
"""

import time
import asyncio
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
import json

from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from sqlalchemy.pool import Pool
import structlog

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger("quantdojo.database.performance")
settings = get_settings()


class QueryPerformanceTracker:
    """Track and analyze database query performance"""
    
    def __init__(self):
        self.slow_queries: List[Dict[str, Any]] = []
        self.query_stats: Dict[str, Dict[str, Any]] = {}
        self.connection_stats: Dict[str, Any] = {
            "total_connections": 0,
            "active_connections": 0,
            "pool_size": 0,
            "checked_out": 0,
            "overflow": 0,
            "invalid": 0
        }
    
    def record_query(self, query: str, duration: float, params: Optional[Dict] = None):
        """Record query execution metrics"""
        query_hash = hash(query)
        
        if query_hash not in self.query_stats:
            self.query_stats[query_hash] = {
                "query": query[:200] + "..." if len(query) > 200 else query,
                "count": 0,
                "total_time": 0.0,
                "avg_time": 0.0,
                "max_time": 0.0,
                "min_time": float('inf')
            }
        
        stats = self.query_stats[query_hash]
        stats["count"] += 1
        stats["total_time"] += duration
        stats["avg_time"] = stats["total_time"] / stats["count"]
        stats["max_time"] = max(stats["max_time"], duration)
        stats["min_time"] = min(stats["min_time"], duration)
        
        # Log slow queries
        if duration > settings.DB_SLOW_QUERY_THRESHOLD:
            slow_query = {
                "query": query,
                "duration": duration,
                "params": params,
                "timestamp": datetime.utcnow().isoformat()
            }
            self.slow_queries.append(slow_query)
            
            if settings.DB_LOG_SLOW_QUERIES:
                logger.warning(
                    "Slow query detected",
                    query=query[:100] + "..." if len(query) > 100 else query,
                    duration=duration,
                    threshold=settings.DB_SLOW_QUERY_THRESHOLD
                )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        # Sort queries by average execution time
        sorted_queries = sorted(
            self.query_stats.values(),
            key=lambda x: x["avg_time"],
            reverse=True
        )
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "connection_stats": self.connection_stats.copy(),
            "query_stats": {
                "total_queries": sum(q["count"] for q in self.query_stats.values()),
                "unique_queries": len(self.query_stats),
                "slow_queries_count": len(self.slow_queries),
                "top_slow_queries": sorted_queries[:10]
            },
            "recent_slow_queries": self.slow_queries[-10:] if self.slow_queries else []
        }
    
    def update_connection_stats(self, pool: Pool):
        """Update connection pool statistics"""
        self.connection_stats.update({
            "pool_size": pool.size(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalidated()
        })


# Global performance tracker instance
performance_tracker = QueryPerformanceTracker()


class DatabasePerformanceMiddleware:
    """Middleware for tracking database performance"""
    
    @asynccontextmanager
    async def track_query(self, query: str, params: Optional[Dict] = None):
        """Context manager to track query execution time"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            performance_tracker.record_query(query, duration, params)


# SQLAlchemy event listeners for performance monitoring
@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query start time"""
    context._query_start_time = time.time()


@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query completion and metrics"""
    if hasattr(context, '_query_start_time'):
        duration = time.time() - context._query_start_time
        performance_tracker.record_query(statement, duration, parameters)


@event.listens_for(Pool, "connect")
def pool_connect(dbapi_connection, connection_record):
    """Track new database connections"""
    performance_tracker.connection_stats["total_connections"] += 1
    logger.debug("New database connection established")


@event.listens_for(Pool, "checkout")
def pool_checkout(dbapi_connection, connection_record, connection_proxy):
    """Track connection checkout from pool"""
    performance_tracker.connection_stats["active_connections"] += 1


@event.listens_for(Pool, "checkin")
def pool_checkin(dbapi_connection, connection_record):
    """Track connection return to pool"""
    performance_tracker.connection_stats["active_connections"] -= 1


class DatabaseOptimizer:
    """Database optimization utilities"""
    
    @staticmethod
    async def analyze_query_performance(session: AsyncSession) -> Dict[str, Any]:
        """Analyze current database query performance using pg_stat_statements"""
        try:
            # Check if pg_stat_statements extension is available
            result = await session.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
                )
            """))
            
            if not result.scalar():
                return {"error": "pg_stat_statements extension not available"}
            
            # Get top slow queries from pg_stat_statements
            result = await session.execute(text("""
                SELECT 
                    query,
                    calls,
                    total_exec_time,
                    mean_exec_time,
                    max_exec_time,
                    rows
                FROM pg_stat_statements 
                WHERE query NOT LIKE '%pg_stat_statements%'
                ORDER BY mean_exec_time DESC 
                LIMIT 10
            """))
            
            slow_queries = []
            for row in result:
                slow_queries.append({
                    "query": row.query[:200] + "..." if len(row.query) > 200 else row.query,
                    "calls": row.calls,
                    "total_time": float(row.total_exec_time),
                    "avg_time": float(row.mean_exec_time),
                    "max_time": float(row.max_exec_time),
                    "rows": row.rows
                })
            
            return {"slow_queries": slow_queries}
            
        except Exception as e:
            logger.error("Error analyzing query performance", error=str(e))
            return {"error": str(e)}
    
    @staticmethod
    async def get_database_stats(session: AsyncSession) -> Dict[str, Any]:
        """Get comprehensive database statistics"""
        try:
            # Database size and connection info
            db_stats = await session.execute(text("""
                SELECT 
                    pg_database_size(current_database()) as db_size,
                    (SELECT count(*) FROM pg_stat_activity WHERE datname = current_database()) as active_connections,
                    (SELECT setting FROM pg_settings WHERE name = 'max_connections') as max_connections
            """))
            
            db_row = db_stats.first()
            
            # Table statistics
            table_stats = await session.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples,
                    last_vacuum,
                    last_autovacuum,
                    last_analyze,
                    last_autoanalyze
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
            """))
            
            tables = []
            for row in table_stats:
                tables.append({
                    "schema": row.schemaname,
                    "table": row.tablename,
                    "inserts": row.inserts,
                    "updates": row.updates,
                    "deletes": row.deletes,
                    "live_tuples": row.live_tuples,
                    "dead_tuples": row.dead_tuples,
                    "last_vacuum": row.last_vacuum.isoformat() if row.last_vacuum else None,
                    "last_analyze": row.last_analyze.isoformat() if row.last_analyze else None
                })
            
            return {
                "database": {
                    "size_bytes": db_row.db_size,
                    "size_mb": round(db_row.db_size / 1024 / 1024, 2),
                    "active_connections": db_row.active_connections,
                    "max_connections": db_row.max_connections
                },
                "tables": tables
            }
            
        except Exception as e:
            logger.error("Error getting database stats", error=str(e))
            return {"error": str(e)}


async def get_performance_metrics() -> Dict[str, Any]:
    """Get current performance metrics"""
    return performance_tracker.get_performance_report()


async def reset_performance_metrics():
    """Reset performance tracking metrics"""
    performance_tracker.slow_queries.clear()
    performance_tracker.query_stats.clear()
    logger.info("Performance metrics reset")
