"""
Database configuration and session management for QuantDojo
Production-optimized with connection pooling and performance monitoring
"""

import os
import asyncio
from typing import AsyncGenerator, Dict, Any
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import create_engine, event, text
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Null<PERSON><PERSON>, Queue<PERSON><PERSON>, StaticPool
from sqlalchemy.orm import sessionmaker


from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger("quantdojo.database")
settings = get_settings()

# Production-optimized connection pool configuration
def get_pool_config() -> Dict[str, Any]:
    """Get optimized connection pool configuration based on environment"""
    if "sqlite" in settings.async_database_url:
        return {
            "poolclass": StaticPool,
            "pool_pre_ping": False,
            "connect_args": {"check_same_thread": False}
        }

    # PostgreSQL production configuration
    return {
        "poolclass": Queue<PERSON>ool,
        "pool_size": settings.DB_POOL_SIZE,
        "max_overflow": settings.DB_MAX_OVERFLOW,
        "pool_timeout": settings.DB_POOL_TIMEOUT,
        "pool_recycle": settings.DB_POOL_RECYCLE,
        "pool_pre_ping": settings.DB_POOL_PRE_PING,
        "pool_reset_on_return": "commit",
        "connect_args": {
            "command_timeout": settings.DB_QUERY_TIMEOUT,
            "server_settings": {
                "statement_timeout": str(settings.DB_STATEMENT_TIMEOUT),
                "lock_timeout": str(settings.DB_LOCK_TIMEOUT),
                "idle_in_transaction_session_timeout": str(settings.DB_IDLE_IN_TRANSACTION_TIMEOUT),
                "application_name": "QuantDojo"
            }
        }
    }

# Create async engine with optimized settings
async_engine = create_async_engine(
    settings.async_database_url,
    echo=settings.DB_ENABLE_QUERY_LOGGING and settings.DEBUG,
    echo_pool=settings.DEBUG,
    **get_pool_config()
)

# Create sync engine for migrations with optimized settings
sync_pool_config = get_pool_config()
if "connect_args" in sync_pool_config:
    # Remove async-specific settings for sync engine
    sync_connect_args = sync_pool_config["connect_args"].copy()
    if "server_settings" in sync_connect_args:
        del sync_connect_args["server_settings"]
    sync_pool_config["connect_args"] = sync_connect_args

sync_engine = create_engine(
    settings.database_url,
    echo=settings.DB_ENABLE_QUERY_LOGGING and settings.DEBUG,
    echo_pool=settings.DEBUG,
    **sync_pool_config
)

# Create session factories
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

SessionLocal = sessionmaker(
    sync_engine,
    autocommit=False,
    autoflush=False,
)


# Database event listeners for optimization and monitoring
@event.listens_for(Engine, "connect")
def set_database_pragmas(dbapi_connection, connection_record):
    """Set database-specific optimizations"""
    connection_string = str(dbapi_connection)

    if "sqlite" in connection_string:
        # SQLite optimizations
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")  # Increased cache
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.execute("PRAGMA mmap_size=268435456")  # 256MB mmap
        cursor.close()
        logger.debug("SQLite pragmas configured")

    elif "postgresql" in connection_string:
        # PostgreSQL session-level optimizations
        cursor = dbapi_connection.cursor()
        try:
            # Set session-level parameters for better performance
            cursor.execute("SET work_mem = '32MB'")
            cursor.execute("SET maintenance_work_mem = '128MB'")
            cursor.execute("SET effective_cache_size = '1GB'")
            cursor.execute("SET random_page_cost = 1.1")
            cursor.execute("SET seq_page_cost = 1.0")
            cursor.execute("SET cpu_tuple_cost = 0.01")
            cursor.execute("SET cpu_index_tuple_cost = 0.005")
            cursor.execute("SET cpu_operator_cost = 0.0025")
            logger.debug("PostgreSQL session parameters configured")
        except Exception as e:
            logger.warning("Could not set PostgreSQL session parameters", error=str(e))
        finally:
            cursor.close()


@event.listens_for(Engine, "first_connect")
def setup_database_monitoring(dbapi_connection, connection_record):
    """Setup database monitoring on first connection"""
    logger.info("Database connection established",
                engine_info=str(connection_record.info))


@event.listens_for(async_engine.sync_engine, "connect")
def log_connection_pool_status(dbapi_connection, connection_record):
    """Log connection pool status for monitoring"""
    pool = async_engine.pool
    logger.debug("Connection pool status",
                pool_size=pool.size(),
                checked_out=pool.checkedout(),
                overflow=pool.overflow(),
                invalid=pool.invalidated())


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Production-optimized async database session with monitoring
    """
    session_start = asyncio.get_event_loop().time()
    session = None

    try:
        session = AsyncSessionLocal()
        logger.debug("Database session created")
        yield session

    except Exception as e:
        logger.error("Database session error",
                    error=str(e),
                    session_duration=asyncio.get_event_loop().time() - session_start)
        if session:
            await session.rollback()
            logger.debug("Database session rolled back")
        raise

    finally:
        if session:
            await session.close()
            session_duration = asyncio.get_event_loop().time() - session_start
            logger.debug("Database session closed",
                        session_duration=session_duration)


def get_sync_session():
    """
    Get synchronous database session (for migrations and admin tasks)
    """
    return SessionLocal()


@asynccontextmanager
async def get_monitored_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get database session with comprehensive monitoring and error handling
    """
    session_id = id(asyncio.current_task())
    start_time = asyncio.get_event_loop().time()

    logger.debug("Starting monitored database session", session_id=session_id)

    async with AsyncSessionLocal() as session:
        try:
            # Set session-level query timeout
            await session.execute(text(f"SET statement_timeout = {settings.DB_QUERY_TIMEOUT * 1000}"))
            yield session

        except asyncio.TimeoutError:
            logger.error("Database session timeout",
                        session_id=session_id,
                        timeout=settings.DB_QUERY_TIMEOUT)
            await session.rollback()
            raise

        except Exception as e:
            logger.error("Database session error",
                        session_id=session_id,
                        error=str(e),
                        error_type=type(e).__name__)
            await session.rollback()
            raise

        finally:
            duration = asyncio.get_event_loop().time() - start_time
            logger.debug("Monitored database session completed",
                        session_id=session_id,
                        duration=duration)


async def init_db():
    """Initialize database tables"""
    async with async_engine.begin() as conn:
        # Import all models to ensure they are registered
        from app.models import user, strategy, backtest, market_data
        from app.models.base import Base

        # Create all tables
        await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")


async def close_db():
    """Close database connections gracefully"""
    logger.info("Closing database connections...")

    try:
        # Close async engine
        await async_engine.dispose()
        logger.debug("Async engine disposed")

        # Close sync engine
        sync_engine.dispose()
        logger.debug("Sync engine disposed")

        logger.info("Database connections closed successfully")

    except Exception as e:
        logger.error("Error closing database connections", error=str(e))
        raise


async def get_connection_pool_stats() -> Dict[str, Any]:
    """Get detailed connection pool statistics"""
    try:
        pool = async_engine.pool

        # Get basic pool stats (available in all pool types)
        pool_size = pool.size()
        checked_out = pool.checkedout()
        overflow = pool.overflow()

        # Try to get invalidated count, fallback to 0 if not available
        try:
            invalid = pool.invalidated()
        except AttributeError:
            invalid = 0

        return {
            "pool_size": pool_size,
            "checked_out": checked_out,
            "overflow": overflow,
            "invalid": invalid,
            "total_connections": pool_size + overflow,
            "available_connections": max(0, pool_size - checked_out),
            "pool_configuration": {
                "pool_size": settings.DB_POOL_SIZE,
                "max_overflow": settings.DB_MAX_OVERFLOW,
                "pool_timeout": settings.DB_POOL_TIMEOUT,
                "pool_recycle": settings.DB_POOL_RECYCLE
            }
        }
    except Exception as e:
        logger.error("Error getting connection pool stats", error=str(e))
        return {"error": str(e)}


async def health_check_database() -> Dict[str, Any]:
    """Comprehensive database health check"""
    health_status = {
        "status": "healthy",
        "timestamp": asyncio.get_event_loop().time(),
        "checks": {}
    }

    try:
        # Test basic connectivity
        async with get_async_session() as session:
            start_time = asyncio.get_event_loop().time()
            result = await session.execute(text("SELECT 1"))
            query_time = asyncio.get_event_loop().time() - start_time

            health_status["checks"]["connectivity"] = {
                "status": "healthy",
                "response_time": query_time
            }

        # Get connection pool stats
        pool_stats = await get_connection_pool_stats()
        health_status["checks"]["connection_pool"] = {
            "status": "healthy" if "error" not in pool_stats else "unhealthy",
            "stats": pool_stats
        }

        # Check for connection pool exhaustion
        if pool_stats.get("available_connections", 0) < 2:
            health_status["checks"]["connection_pool"]["status"] = "warning"
            health_status["checks"]["connection_pool"]["message"] = "Low available connections"

        # Overall status
        if any(check["status"] == "unhealthy" for check in health_status["checks"].values()):
            health_status["status"] = "unhealthy"
        elif any(check["status"] == "warning" for check in health_status["checks"].values()):
            health_status["status"] = "warning"

    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        health_status["status"] = "unhealthy"
        health_status["error"] = str(e)

    return health_status


async def optimize_connection_pool():
    """Optimize connection pool based on current usage patterns"""
    try:
        pool_stats = await get_connection_pool_stats()

        # Check if there was an error getting pool stats
        if "error" in pool_stats:
            return pool_stats

        # Log current pool utilization
        utilization = (pool_stats["checked_out"] / pool_stats["pool_size"]) * 100

        logger.info("Connection pool utilization",
                   utilization_percent=utilization,
                   checked_out=pool_stats["checked_out"],
                   pool_size=pool_stats["pool_size"],
                   overflow=pool_stats["overflow"])

        # Recommendations based on utilization
        if utilization > 80:
            logger.warning("High connection pool utilization detected",
                          recommendation="Consider increasing pool_size or max_overflow")
        elif utilization < 20:
            logger.info("Low connection pool utilization",
                       recommendation="Pool size may be optimized for better resource usage")

        return pool_stats

    except Exception as e:
        logger.error("Error optimizing connection pool", error=str(e))
        return {"error": str(e)}
