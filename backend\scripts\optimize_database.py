#!/usr/bin/env python3
"""
Database optimization script for QuantDojo production deployment
Applies PostgreSQL-specific optimizations and performance tuning
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, Any, List

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_async_session, get_connection_pool_stats
from app.core.database_performance import DatabaseOptimizer, get_performance_metrics
from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger("quantdojo.database.optimizer")
settings = get_settings()


class DatabaseOptimizationManager:
    """Comprehensive database optimization manager"""
    
    def __init__(self):
        self.optimization_results = []
    
    async def apply_postgresql_optimizations(self, session: AsyncSession) -> Dict[str, Any]:
        """Apply PostgreSQL-specific performance optimizations"""
        optimizations = []
        
        try:
            # Enable pg_stat_statements extension for query monitoring
            await session.execute(text("CREATE EXTENSION IF NOT EXISTS pg_stat_statements"))
            optimizations.append("pg_stat_statements extension enabled")
            
            # Enable auto_explain for slow query logging
            await session.execute(text("LOAD 'auto_explain'"))
            optimizations.append("auto_explain module loaded")
            
            # Set optimal PostgreSQL configuration parameters
            config_params = {
                "shared_preload_libraries": "'pg_stat_statements,auto_explain'",
                "max_connections": "200",
                "shared_buffers": "'256MB'",
                "effective_cache_size": "'1GB'",
                "maintenance_work_mem": "'128MB'",
                "checkpoint_completion_target": "0.9",
                "wal_buffers": "'16MB'",
                "default_statistics_target": "100",
                "random_page_cost": "1.1",
                "effective_io_concurrency": "200",
                "work_mem": "'32MB'",
                "min_wal_size": "'1GB'",
                "max_wal_size": "'4GB'",
                "max_worker_processes": "8",
                "max_parallel_workers_per_gather": "4",
                "max_parallel_workers": "8",
                "max_parallel_maintenance_workers": "4",
                # Auto-explain settings for slow query logging
                "auto_explain.log_min_duration": "'1000ms'",  # Log queries > 1 second
                "auto_explain.log_analyze": "true",
                "auto_explain.log_buffers": "true",
                "auto_explain.log_timing": "true",
                "auto_explain.log_triggers": "true",
                "auto_explain.log_verbose": "true",
                "auto_explain.log_nested_statements": "true"
            }
            
            for param, value in config_params.items():
                try:
                    await session.execute(text(f"ALTER SYSTEM SET {param} = {value}"))
                    optimizations.append(f"Set {param} = {value}")
                except Exception as e:
                    logger.warning(f"Could not set {param}", error=str(e))
            
            # Reload configuration
            await session.execute(text("SELECT pg_reload_conf()"))
            optimizations.append("PostgreSQL configuration reloaded")
            
            await session.commit()
            
            return {
                "status": "success",
                "optimizations_applied": optimizations,
                "count": len(optimizations)
            }
            
        except Exception as e:
            await session.rollback()
            logger.error("Error applying PostgreSQL optimizations", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "optimizations_applied": optimizations
            }
    
    async def optimize_table_statistics(self, session: AsyncSession) -> Dict[str, Any]:
        """Update table statistics for better query planning"""
        try:
            tables = ["users", "strategies", "backtests", "market_data"]
            results = []
            
            for table in tables:
                # Update table statistics
                await session.execute(text(f"ANALYZE {table}"))
                results.append(f"Analyzed table: {table}")
                
                # Get table statistics
                stats_query = text("""
                    SELECT 
                        schemaname, tablename, n_live_tup, n_dead_tup,
                        last_vacuum, last_autovacuum, last_analyze, last_autoanalyze
                    FROM pg_stat_user_tables 
                    WHERE tablename = :table_name
                """)
                
                result = await session.execute(stats_query, {"table_name": table})
                row = result.first()
                
                if row:
                    results.append({
                        "table": table,
                        "live_tuples": row.n_live_tup,
                        "dead_tuples": row.n_dead_tup,
                        "last_analyze": row.last_analyze.isoformat() if row.last_analyze else None
                    })
            
            await session.commit()
            
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            await session.rollback()
            logger.error("Error optimizing table statistics", error=str(e))
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def create_maintenance_procedures(self, session: AsyncSession) -> Dict[str, Any]:
        """Create stored procedures for database maintenance"""
        try:
            procedures = []
            
            # Create procedure for automatic VACUUM and ANALYZE
            vacuum_procedure = """
            CREATE OR REPLACE FUNCTION maintain_database()
            RETURNS void AS $$
            BEGIN
                -- Vacuum and analyze all user tables
                VACUUM ANALYZE users;
                VACUUM ANALYZE strategies;
                VACUUM ANALYZE backtests;
                VACUUM ANALYZE market_data;
                
                -- Log maintenance completion
                RAISE NOTICE 'Database maintenance completed at %', NOW();
            END;
            $$ LANGUAGE plpgsql;
            """
            
            await session.execute(text(vacuum_procedure))
            procedures.append("maintain_database() procedure created")
            
            # Create procedure for index maintenance
            index_procedure = """
            CREATE OR REPLACE FUNCTION reindex_tables()
            RETURNS void AS $$
            BEGIN
                -- Reindex all tables
                REINDEX TABLE users;
                REINDEX TABLE strategies;
                REINDEX TABLE backtests;
                REINDEX TABLE market_data;
                
                RAISE NOTICE 'Index maintenance completed at %', NOW();
            END;
            $$ LANGUAGE plpgsql;
            """
            
            await session.execute(text(index_procedure))
            procedures.append("reindex_tables() procedure created")
            
            # Create procedure for performance monitoring
            monitoring_procedure = """
            CREATE OR REPLACE FUNCTION get_performance_summary()
            RETURNS TABLE(
                metric_name text,
                metric_value text
            ) AS $$
            BEGIN
                RETURN QUERY
                SELECT 'Database Size'::text, pg_size_pretty(pg_database_size(current_database()))::text
                UNION ALL
                SELECT 'Active Connections'::text, count(*)::text FROM pg_stat_activity WHERE state = 'active'
                UNION ALL
                SELECT 'Total Connections'::text, count(*)::text FROM pg_stat_activity
                UNION ALL
                SELECT 'Cache Hit Ratio'::text, 
                       round((sum(blks_hit) * 100.0 / sum(blks_hit + blks_read)), 2)::text || '%'
                FROM pg_stat_database WHERE datname = current_database();
            END;
            $$ LANGUAGE plpgsql;
            """
            
            await session.execute(text(monitoring_procedure))
            procedures.append("get_performance_summary() procedure created")
            
            await session.commit()
            
            return {
                "status": "success",
                "procedures_created": procedures,
                "count": len(procedures)
            }
            
        except Exception as e:
            await session.rollback()
            logger.error("Error creating maintenance procedures", error=str(e))
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def run_comprehensive_optimization(self) -> Dict[str, Any]:
        """Run comprehensive database optimization"""
        results = {
            "timestamp": asyncio.get_event_loop().time(),
            "optimizations": {}
        }
        
        try:
            async for session in get_async_session():
                # Apply PostgreSQL optimizations
                logger.info("Applying PostgreSQL optimizations...")
                pg_result = await self.apply_postgresql_optimizations(session)
                results["optimizations"]["postgresql"] = pg_result
                
                # Optimize table statistics
                logger.info("Optimizing table statistics...")
                stats_result = await self.optimize_table_statistics(session)
                results["optimizations"]["statistics"] = stats_result
                
                # Create maintenance procedures
                logger.info("Creating maintenance procedures...")
                procedures_result = await self.create_maintenance_procedures(session)
                results["optimizations"]["procedures"] = procedures_result
                
                break  # Exit the async generator
            
            # Get performance metrics
            logger.info("Collecting performance metrics...")
            performance_metrics = await get_performance_metrics()
            results["performance_metrics"] = performance_metrics
            
            # Get connection pool stats
            pool_stats = await get_connection_pool_stats()
            results["connection_pool"] = pool_stats
            
            results["status"] = "success"
            results["message"] = "Database optimization completed successfully"
            
        except Exception as e:
            logger.error("Error during comprehensive optimization", error=str(e))
            results["status"] = "error"
            results["error"] = str(e)
        
        return results


async def main():
    """Main optimization script"""
    print("🚀 Starting QuantDojo Database Optimization...")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Database URL: {settings.database_url}")
    print("-" * 50)
    
    optimizer = DatabaseOptimizationManager()
    
    try:
        results = await optimizer.run_comprehensive_optimization()
        
        print("\n📊 Optimization Results:")
        print(f"Status: {results['status']}")
        
        if results["status"] == "success":
            print(f"✅ {results['message']}")
            
            # Print optimization details
            for category, result in results["optimizations"].items():
                print(f"\n{category.upper()}:")
                if result["status"] == "success":
                    if "optimizations_applied" in result:
                        for opt in result["optimizations_applied"]:
                            print(f"  ✅ {opt}")
                    elif "procedures_created" in result:
                        for proc in result["procedures_created"]:
                            print(f"  ✅ {proc}")
                else:
                    print(f"  ❌ Error: {result.get('error', 'Unknown error')}")
            
            # Print performance summary
            if "connection_pool" in results and "error" not in results["connection_pool"]:
                pool = results["connection_pool"]
                print(f"\n🔗 Connection Pool Status:")
                print(f"  Pool Size: {pool['pool_size']}")
                print(f"  Active: {pool['checked_out']}")
                print(f"  Available: {pool['available_connections']}")
                print(f"  Overflow: {pool['overflow']}")
        
        else:
            print(f"❌ Optimization failed: {results.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Fatal error during optimization: {str(e)}")
        return 1
    
    print("\n🎉 Database optimization completed!")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
