version: '3.8'

services:
  # Production PostgreSQL with optimized settings
  postgres:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports: []  # Remove port exposure in production

  # Production Redis with optimized settings
  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports: []  # Remove port exposure in production

  # Production Backend
  backend:
    build:
      target: production
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - ENVIRONMENT=production
      - LOG_LEVEL=WARNING
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    ports: []  # Remove direct port exposure, use nginx proxy

  # Production Celery Worker with scaling
  celery-worker:
    build:
      target: production
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - ENVIRONMENT=production
      - LOG_LEVEL=WARNING
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    deploy:
      replicas: 2

  # Production Celery Beat
  celery-beat:
    build:
      target: production
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - ENVIRONMENT=production
      - LOG_LEVEL=WARNING
    volumes:
      - backend_logs:/app/logs

  # Production Frontend
  frontend:
    build:
      target: production
    environment:
      - VITE_API_URL=${VITE_API_URL}
      - VITE_ENVIRONMENT=production
    volumes: []  # Remove volume mounts in production
    ports: []  # Remove direct port exposure, use nginx proxy

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quantdojo-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - backend_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - quantdojo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: quantdojo-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - quantdojo-network
    restart: unless-stopped

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: quantdojo-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    networks:
      - quantdojo-network
    restart: unless-stopped

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
