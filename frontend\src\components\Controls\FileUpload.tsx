import React, { useRef, useState } from 'react';
import { Upload, FileText, X } from 'lucide-react';
import { useBacktestStore } from '../../store/backtestStore';
import { uploadCSVData } from '../../utils/api';
import toast from 'react-hot-toast';

export const FileUpload: React.FC = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const { setLoading, setError } = useBacktestStore();

  const handleFileSelect = (file: File) => {
    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('File size must be less than 10MB');
      return;
    }

    setUploadedFile(file);
  };

  const handleFileUpload = async () => {
    if (!uploadedFile) return;

    setIsUploading(true);
    setLoading(true);
    setError(null);

    try {
      const result = await uploadCSVData(uploadedFile);
      
      // Update the store with the uploaded data
      useBacktestStore.setState({
        marketData: result,
        symbol: 'CUSTOM',
      });

      toast.success('CSV data uploaded successfully!');
      setUploadedFile(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to upload CSV file';
      toast.error(message);
      setError(message);
    } finally {
      setIsUploading(false);
      setLoading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const clearFile = () => {
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-4">
      {/* File Drop Zone */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragging 
            ? 'border-primary-500 bg-primary-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleInputChange}
          className="hidden"
        />

        <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm text-gray-600 mb-1">
          Drop your CSV file here, or click to browse
        </p>
        <p className="text-xs text-gray-500">
          Supports CSV files up to 10MB
        </p>
      </div>

      {/* File Info */}
      {uploadedFile && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-gray-500" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {uploadedFile.name}
              </p>
              <p className="text-xs text-gray-500">
                {(uploadedFile.size / 1024).toFixed(1)} KB
              </p>
            </div>
          </div>
          <button
            onClick={clearFile}
            className="text-gray-400 hover:text-gray-600"
            disabled={isUploading}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Upload Button */}
      {uploadedFile && (
        <button
          onClick={handleFileUpload}
          disabled={isUploading}
          className="btn-primary btn-sm w-full flex items-center justify-center space-x-2"
        >
          {isUploading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <Upload className="h-4 w-4" />
              <span>Upload CSV Data</span>
            </>
          )}
        </button>
      )}

      {/* Format Instructions */}
      <div className="text-xs text-gray-500 space-y-1">
        <p className="font-medium">Expected CSV format:</p>
        <p>Date,Open,High,Low,Close,Volume</p>
        <p>2023-01-01,100.00,105.00,99.00,104.00,1000000</p>
      </div>
    </div>
  );
};
