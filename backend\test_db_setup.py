#!/usr/bin/env python3
"""
Test script to verify database setup and migrations
"""

import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test that all imports work correctly"""
    try:
        print("Testing imports...")
        
        # Test configuration
        from app.core.config import get_settings
        settings = get_settings()
        print(f"✓ Configuration loaded: {settings.PROJECT_NAME}")
        
        # Test database models
        from app.models.base import Base, BaseModel
        from app.models.user import User, UserTier
        from app.models.strategy import Strategy
        from app.models.backtest import Backtest
        from app.models.market_data import MarketData
        print("✓ All models imported successfully")
        
        # Test database connection setup
        from app.core.database import async_engine, sync_engine
        print("✓ Database engines created")
        
        # Test Alembic configuration
        from alembic.config import Config
        alembic_cfg = Config("alembic.ini")
        print("✓ Alembic configuration loaded")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False


def test_model_creation():
    """Test that models can be instantiated"""
    try:
        print("\nTesting model creation...")
        
        from app.models.user import User, UserTier
        from app.core.auth import PasswordManager
        
        password_manager = PasswordManager()
        
        # Create a test user instance (not saved to DB)
        user = User(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            hashed_password=password_manager.hash_password("testpass123"),
            tier=UserTier.FREE
        )
        
        print(f"✓ User model created: {user.username}")
        
        from app.models.strategy import Strategy
        
        strategy = Strategy(
            name="Test Strategy",
            description="A test trading strategy",
            code="# Test strategy code",
            user_id=1,
            is_active=True,
            is_public=False
        )
        
        print(f"✓ Strategy model created: {strategy.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        return False


def test_database_url():
    """Test database URL configuration"""
    try:
        print("\nTesting database configuration...")
        
        from app.core.config import get_settings
        settings = get_settings()
        
        print(f"Database URL: {settings.database_url}")
        print(f"Async Database URL: {settings.async_database_url}")
        
        # Test URL parsing
        if "postgresql" in settings.database_url:
            print("✓ PostgreSQL database configured")
        elif "sqlite" in settings.database_url:
            print("✓ SQLite database configured")
        else:
            print("? Unknown database type")
            
        return True
        
    except Exception as e:
        print(f"✗ Database URL test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("QuantDojo Database Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_model_creation,
        test_database_url,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Database setup is ready.")
        return 0
    else:
        print("✗ Some tests failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
