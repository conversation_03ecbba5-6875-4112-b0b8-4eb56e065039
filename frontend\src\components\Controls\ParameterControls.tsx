import React from 'react';
import { useBacktestStore } from '../../store/backtestStore';
import { MovingAverageParams, RSIParams } from '../../types';

export const ParameterControls: React.FC = () => {
  const { selectedStrategy, strategyParams, setStrategyParams, backtestConfig, setBacktestConfig } = useBacktestStore();

  const handleParameterChange = (key: string, value: any) => {
    setStrategyParams({
      ...strategyParams,
      [key]: value,
    });
  };

  const handleBacktestConfigChange = (key: string, value: any) => {
    setBacktestConfig({
      [key]: value,
    });
  };

  const renderMovingAverageControls = () => {
    const params = strategyParams as MovingAverageParams;
    
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fast Period: {params.fast_period}
          </label>
          <input
            type="range"
            min="1"
            max="100"
            value={params.fast_period}
            onChange={(e) => handleParameterChange('fast_period', parseInt(e.target.value))}
            className="slider w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>1</span>
            <span>100</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Slow Period: {params.slow_period}
          </label>
          <input
            type="range"
            min="2"
            max="200"
            value={params.slow_period}
            onChange={(e) => handleParameterChange('slow_period', parseInt(e.target.value))}
            className="slider w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>2</span>
            <span>200</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Moving Average Type
          </label>
          <select
            value={params.ma_type}
            onChange={(e) => handleParameterChange('ma_type', e.target.value)}
            className="select w-full"
          >
            <option value="sma">Simple Moving Average (SMA)</option>
            <option value="ema">Exponential Moving Average (EMA)</option>
          </select>
        </div>
      </div>
    );
  };

  const renderRSIControls = () => {
    const params = strategyParams as RSIParams;
    
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            RSI Period: {params.period}
          </label>
          <input
            type="range"
            min="2"
            max="50"
            value={params.period}
            onChange={(e) => handleParameterChange('period', parseInt(e.target.value))}
            className="slider w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>2</span>
            <span>50</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Overbought Level: {params.overbought}
          </label>
          <input
            type="range"
            min="50"
            max="100"
            value={params.overbought}
            onChange={(e) => handleParameterChange('overbought', parseFloat(e.target.value))}
            className="slider w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>50</span>
            <span>100</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Oversold Level: {params.oversold}
          </label>
          <input
            type="range"
            min="0"
            max="50"
            value={params.oversold}
            onChange={(e) => handleParameterChange('oversold', parseFloat(e.target.value))}
            className="slider w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0</span>
            <span>50</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Strategy Parameters */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Strategy Parameters</h4>
        {selectedStrategy === 'moving_average' ? renderMovingAverageControls() : renderRSIControls()}
      </div>

      {/* Backtest Configuration */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Backtest Configuration</h4>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Initial Capital
            </label>
            <input
              type="number"
              min="1000"
              max="10000000"
              step="1000"
              value={backtestConfig.initial_capital}
              onChange={(e) => handleBacktestConfigChange('initial_capital', parseFloat(e.target.value))}
              className="input w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Commission Rate: {(backtestConfig.commission * 100).toFixed(3)}%
            </label>
            <input
              type="range"
              min="0"
              max="0.01"
              step="0.0001"
              value={backtestConfig.commission}
              onChange={(e) => handleBacktestConfigChange('commission', parseFloat(e.target.value))}
              className="slider w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>1%</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Slippage Rate: {(backtestConfig.slippage * 100).toFixed(3)}%
            </label>
            <input
              type="range"
              min="0"
              max="0.01"
              step="0.0001"
              value={backtestConfig.slippage}
              onChange={(e) => handleBacktestConfigChange('slippage', parseFloat(e.target.value))}
              className="slider w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>1%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
