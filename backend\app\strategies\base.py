"""
Base strategy class for QuantDojo
"""

from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, Any, Tuple, List
from app.core.models import OHLCVData

class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies
    """
    
    def __init__(self, parameters: Dict[str, Any]):
        """
        Initialize strategy with parameters
        
        Args:
            parameters: Strategy-specific parameters
        """
        self.parameters = parameters
        self.name = self.__class__.__name__
    
    @abstractmethod
    def generate_signals(self, data: List[OHLCVData]) -> Tuple[pd.Series, Dict[str, List[Dict[str, Any]]]]:
        """
        Generate trading signals from OHLCV data
        
        Args:
            data: List of OHLCV data points
            
        Returns:
            Tuple of (signals, indicators)
            - signals: Series with 1 for buy, -1 for sell, 0 for hold
            - indicators: Dictionary of indicator data for charting
        """
        pass
    
    @abstractmethod
    def get_parameter_schema(self) -> Dict[str, Any]:
        """
        Get the parameter schema for this strategy
        
        Returns:
            Dictionary describing the parameters
        """
        pass
    
    def _convert_data_to_df(self, data: List[OHLCVData]) -> pd.DataFrame:
        """
        Convert OHLCV data to pandas DataFrame
        
        Args:
            data: List of OHLCV data points
            
        Returns:
            DataFrame with OHLCV data
        """
        df = pd.DataFrame([
            {
                'timestamp': d.timestamp,
                'open': d.open,
                'high': d.high,
                'low': d.low,
                'close': d.close,
                'volume': d.volume
            }
            for d in data
        ])
        df.set_index('timestamp', inplace=True)
        return df
    
    def _calculate_sma(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Simple Moving Average"""
        return prices.rolling(window=period).mean()
    
    def _calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return prices.ewm(span=period).mean()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _format_indicator_data(self, df: pd.DataFrame, column: str, name: str) -> List[Dict[str, Any]]:
        """
        Format indicator data for frontend consumption
        
        Args:
            df: DataFrame containing the data
            column: Column name in the DataFrame
            name: Display name for the indicator
            
        Returns:
            List of dictionaries with timestamp and value
        """
        return [
            {
                'timestamp': timestamp.isoformat(),
                'value': float(value) if pd.notna(value) else None,
                'name': name
            }
            for timestamp, value in df[column].items()
            if pd.notna(value)
        ]
