"""Add performance indexes and optimizations

Revision ID: b054a82e5531
Revises: 0296ff567c2a
Create Date: 2025-07-07 22:58:01.331469

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b054a82e5531'
down_revision: Union[str, None] = '0296ff567c2a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add comprehensive performance indexes for production workloads"""

    # Market Data Performance Indexes
    # Composite index for symbol + timestamp range queries (most common query pattern)
    op.create_index(
        'idx_market_data_symbol_timestamp_composite',
        'market_data',
        ['symbol', 'timestamp'],
        unique=False
    )

    # Index for timestamp-only queries (time series analysis)
    op.create_index(
        'idx_market_data_timestamp',
        'market_data',
        ['timestamp'],
        unique=False
    )

    # Index for volume-based queries (high volume filtering)
    op.create_index(
        'idx_market_data_volume',
        'market_data',
        ['volume'],
        unique=False
    )

    # Partial index for recent data (last 30 days) - most frequently accessed
    op.execute("""
        CREATE INDEX idx_market_data_recent
        ON market_data (symbol, timestamp DESC)
        WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
    """)

    # User Authentication & Authorization Indexes
    # Composite index for user lookup by email + active status
    op.create_index(
        'idx_users_email_active',
        'users',
        ['email', 'is_active'],
        unique=False
    )

    # Index for username lookups (login)
    op.create_index(
        'idx_users_username_active',
        'users',
        ['username', 'is_active'],
        unique=False
    )

    # Index for user tier-based queries (subscription management)
    op.create_index(
        'idx_users_tier_active',
        'users',
        ['tier', 'is_active'],
        unique=False
    )

    # Strategy Management Indexes
    # Composite index for user's strategies
    op.create_index(
        'idx_strategies_user_active',
        'strategies',
        ['user_id', 'is_active'],
        unique=False
    )

    # Index for public strategy discovery
    op.create_index(
        'idx_strategies_public_active',
        'strategies',
        ['is_public', 'is_active'],
        unique=False
    )

    # Index for strategy name searches
    op.create_index(
        'idx_strategies_name',
        'strategies',
        ['name'],
        unique=False
    )

    # Backtest Performance Indexes
    # Composite index for user's backtests
    op.create_index(
        'idx_backtests_user_created',
        'backtests',
        ['user_id', 'created_at'],
        unique=False
    )

    # Index for strategy-specific backtests
    op.create_index(
        'idx_backtests_strategy_created',
        'backtests',
        ['strategy_id', 'created_at'],
        unique=False
    )

    # Index for symbol-based backtest queries
    op.create_index(
        'idx_backtests_symbol',
        'backtests',
        ['symbol'],
        unique=False
    )

    # Partial index for successful backtests (most commonly accessed)
    op.execute("""
        CREATE INDEX idx_backtests_successful
        ON backtests (user_id, created_at DESC)
        WHERE status = 'completed'
    """)

    # Performance Analytics Indexes
    # Index for ROI-based sorting and filtering
    op.create_index(
        'idx_backtests_roi',
        'backtests',
        ['total_return'],
        unique=False
    )

    # Index for Sharpe ratio analysis
    op.create_index(
        'idx_backtests_sharpe',
        'backtests',
        ['sharpe_ratio'],
        unique=False
    )

    # Composite index for performance ranking
    op.create_index(
        'idx_backtests_performance_ranking',
        'backtests',
        ['total_return', 'sharpe_ratio', 'max_drawdown'],
        unique=False
    )

    # Time-based Analysis Indexes
    # Index for created_at across all tables for time-series analysis
    op.create_index(
        'idx_users_created_at',
        'users',
        ['created_at'],
        unique=False
    )

    op.create_index(
        'idx_strategies_created_at',
        'strategies',
        ['created_at'],
        unique=False
    )

    # PostgreSQL-specific optimizations
    op.execute("ANALYZE market_data")
    op.execute("ANALYZE users")
    op.execute("ANALYZE strategies")
    op.execute("ANALYZE backtests")


def downgrade() -> None:
    """Remove performance indexes"""

    # Drop all created indexes
    op.drop_index('idx_market_data_symbol_timestamp_composite', 'market_data')
    op.drop_index('idx_market_data_timestamp', 'market_data')
    op.drop_index('idx_market_data_volume', 'market_data')
    op.execute("DROP INDEX IF EXISTS idx_market_data_recent")

    op.drop_index('idx_users_email_active', 'users')
    op.drop_index('idx_users_username_active', 'users')
    op.drop_index('idx_users_tier_active', 'users')

    op.drop_index('idx_strategies_user_active', 'strategies')
    op.drop_index('idx_strategies_public_active', 'strategies')
    op.drop_index('idx_strategies_name', 'strategies')

    op.drop_index('idx_backtests_user_created', 'backtests')
    op.drop_index('idx_backtests_strategy_created', 'backtests')
    op.drop_index('idx_backtests_symbol', 'backtests')
    op.execute("DROP INDEX IF EXISTS idx_backtests_successful")

    op.drop_index('idx_backtests_roi', 'backtests')
    op.drop_index('idx_backtests_sharpe', 'backtests')
    op.drop_index('idx_backtests_performance_ranking', 'backtests')

    op.drop_index('idx_users_created_at', 'users')
    op.drop_index('idx_strategies_created_at', 'strategies')
