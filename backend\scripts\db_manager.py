#!/usr/bin/env python3
"""
Database management script for QuantDojo
Provides utilities for database operations, migrations, and maintenance
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine
from alembic.config import Config
from alembic import command
from app.core.config import get_settings
from app.models.base import Base


def get_alembic_config():
    """Get Alembic configuration"""
    alembic_cfg = Config(os.path.join(os.path.dirname(__file__), "..", "alembic.ini"))
    return alembic_cfg


def create_database():
    """Create the database if it doesn't exist"""
    settings = get_settings()
    
    # Parse database URL to get connection details
    db_url = settings.database_url
    if "postgresql" in db_url:
        # Extract database name from URL
        db_name = db_url.split("/")[-1].split("?")[0]
        base_url = db_url.rsplit("/", 1)[0]
        
        # Connect to postgres database to create our database
        postgres_url = f"{base_url}/postgres"
        engine = create_engine(postgres_url)
        
        try:
            with engine.connect() as conn:
                # Check if database exists
                result = conn.execute(
                    text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                    {"db_name": db_name}
                )
                
                if not result.fetchone():
                    # Create database
                    conn.execute(text("COMMIT"))  # End any existing transaction
                    conn.execute(text(f"CREATE DATABASE {db_name}"))
                    print(f"Database '{db_name}' created successfully")
                else:
                    print(f"Database '{db_name}' already exists")
                    
        except Exception as e:
            print(f"Error creating database: {e}")
        finally:
            engine.dispose()


def run_migrations():
    """Run database migrations"""
    try:
        alembic_cfg = get_alembic_config()
        command.upgrade(alembic_cfg, "head")
        print("Migrations completed successfully")
    except Exception as e:
        print(f"Error running migrations: {e}")


def create_migration(message: str):
    """Create a new migration"""
    try:
        alembic_cfg = get_alembic_config()
        command.revision(alembic_cfg, message=message, autogenerate=True)
        print(f"Migration '{message}' created successfully")
    except Exception as e:
        print(f"Error creating migration: {e}")


def reset_database():
    """Reset database by dropping and recreating all tables"""
    settings = get_settings()
    engine = create_engine(settings.database_url)
    
    try:
        # Drop all tables
        Base.metadata.drop_all(bind=engine)
        print("All tables dropped")
        
        # Run migrations to recreate tables
        run_migrations()
        print("Database reset completed")
        
    except Exception as e:
        print(f"Error resetting database: {e}")
    finally:
        engine.dispose()


def check_connection():
    """Check database connection"""
    settings = get_settings()
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                print("Database connection successful")
                return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False
    finally:
        engine.dispose()


async def init_database():
    """Initialize database with sample data"""
    from app.models.user import User, UserTier
    from app.core.auth import PasswordManager
    from app.core.database import get_async_session
    
    password_manager = PasswordManager()
    
    async for session in get_async_session():
        try:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                full_name="QuantDojo Admin",
                hashed_password=password_manager.hash_password("admin123"),
                is_active=True,
                is_verified=True,
                is_superuser=True,
                tier=UserTier.ENTERPRISE
            )
            
            session.add(admin_user)
            await session.commit()
            print("Admin user created successfully")
            
        except Exception as e:
            print(f"Error initializing database: {e}")
            await session.rollback()
        finally:
            await session.close()
            break


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="QuantDojo Database Manager")
    parser.add_argument("command", choices=[
        "create", "migrate", "reset", "check", "init", "new-migration"
    ], help="Database command to execute")
    parser.add_argument("--message", "-m", help="Migration message (for new-migration)")
    
    args = parser.parse_args()
    
    if args.command == "create":
        create_database()
    elif args.command == "migrate":
        run_migrations()
    elif args.command == "reset":
        reset_database()
    elif args.command == "check":
        check_connection()
    elif args.command == "init":
        asyncio.run(init_database())
    elif args.command == "new-migration":
        if not args.message:
            print("Error: Migration message is required for new-migration command")
            sys.exit(1)
        create_migration(args.message)


if __name__ == "__main__":
    main()
