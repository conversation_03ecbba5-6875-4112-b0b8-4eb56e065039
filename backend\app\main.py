"""
QuantDojo FastAPI Application Entry Point
"""

import os
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

from app.api import data, backtest, strategies, health, auth
from app.core.logging import LoggingMiddleware, get_logger
from app.core.database import init_db, close_db
from app.core.monitoring import shutdown_handler, metrics
from app.core.security import limiter, SecurityMiddleware
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded

# Initialize logger
logger = get_logger("quantdojo.main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("QuantDojo API starting up", version="1.0.0")

    # Setup signal handlers for graceful shutdown
    shutdown_handler.setup_signal_handlers()

    # Add database cleanup to shutdown handler
    shutdown_handler.add_cleanup_callback(close_db)

    try:
        await init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise

    logger.info("QuantDojo API startup completed successfully")

    yield

    # Shutdown
    logger.info("QuantDojo API shutting down")
    await shutdown_handler.shutdown()

app = FastAPI(
    title="QuantDojo API",
    description="Interactive Trading Strategy Backtester API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add rate limiter state
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Add security middleware first
app.add_middleware(SecurityMiddleware)

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Configure CORS
allowed_origins = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://localhost:80",
    "https://localhost:443",
]

# Add production origins from environment
if production_origin := os.getenv("FRONTEND_URL"):
    allowed_origins.append(production_origin)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler with logging"""
    logger.error(
        "Unhandled exception",
        error=str(exc),
        error_type=type(exc).__name__,
        path=request.url.path,
        method=request.method
    )

    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

# Include API routers
app.include_router(health.router, tags=["health"])
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(data.router, prefix="/api/data", tags=["data"])
app.include_router(backtest.router, prefix="/api/backtest", tags=["backtest"])
app.include_router(strategies.router, prefix="/api/strategies", tags=["strategies"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to QuantDojo API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development")
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
