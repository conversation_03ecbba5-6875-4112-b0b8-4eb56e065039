"""
Core backtesting engine for QuantDojo
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

from app.core.models import OHLCVData, Trade, BacktestResult, PerformanceMetrics
from app.utils.metrics import calculate_performance_metrics

class BacktestEngine:
    """
    Vectorized backtesting engine
    """
    
    def __init__(self, 
                 data: List[OHLCVData],
                 initial_capital: float = 10000.0,
                 commission: float = 0.001,
                 slippage: float = 0.001):
        """
        Initialize the backtesting engine
        
        Args:
            data: OHLCV data
            initial_capital: Starting capital
            commission: Commission rate (as decimal)
            slippage: Slippage rate (as decimal)
        """
        self.data = data
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        
        # Convert to DataFrame for easier manipulation
        self.df = pd.DataFrame([
            {
                'timestamp': d.timestamp,
                'open': d.open,
                'high': d.high,
                'low': d.low,
                'close': d.close,
                'volume': d.volume
            }
            for d in data
        ])
        self.df.set_index('timestamp', inplace=True)
        
        # Initialize tracking variables
        self.position = 0  # Current position size
        self.cash = initial_capital
        self.trades: List[Trade] = []
        self.equity_curve: List[Dict[str, Any]] = []
        
    def execute_backtest(self, signals: pd.Series, strategy_name: str, parameters: Dict[str, Any]) -> BacktestResult:
        """
        Execute backtest with given signals
        
        Args:
            signals: Series with 1 for buy, -1 for sell, 0 for hold
            strategy_name: Name of the strategy
            parameters: Strategy parameters
            
        Returns:
            BacktestResult object
        """
        # Reset state
        self.position = 0
        self.cash = self.initial_capital
        self.trades = []
        self.equity_curve = []
        
        current_trade: Optional[Trade] = None
        
        for i, (timestamp, signal) in enumerate(signals.items()):
            price = self.df.loc[timestamp, 'close']
            
            # Calculate current equity
            portfolio_value = self.cash + (self.position * price)
            self.equity_curve.append({
                'timestamp': timestamp,
                'equity': portfolio_value,
                'cash': self.cash,
                'position_value': self.position * price
            })
            
            # Process signals
            if signal == 1 and self.position <= 0:  # Buy signal
                if current_trade and current_trade.side == "sell":
                    # Close short position
                    self._close_trade(current_trade, timestamp, price)
                    current_trade = None
                
                # Open long position
                current_trade = self._open_trade("buy", timestamp, price)
                
            elif signal == -1 and self.position >= 0:  # Sell signal
                if current_trade and current_trade.side == "buy":
                    # Close long position
                    self._close_trade(current_trade, timestamp, price)
                    current_trade = None
                
                # Open short position
                current_trade = self._open_trade("sell", timestamp, price)
        
        # Close any remaining open trade
        if current_trade:
            final_price = self.df.iloc[-1]['close']
            final_timestamp = self.df.index[-1]
            self._close_trade(current_trade, final_timestamp, final_price)
        
        # Calculate final equity
        final_price = self.df.iloc[-1]['close']
        final_equity = self.cash + (self.position * final_price)
        
        # Calculate performance metrics
        performance_metrics = calculate_performance_metrics(
            self.trades, 
            self.equity_curve, 
            self.initial_capital
        )
        
        return BacktestResult(
            symbol=parameters.get('symbol', 'UNKNOWN'),
            strategy=strategy_name,
            parameters=parameters,
            start_date=self.df.index[0],
            end_date=self.df.index[-1],
            initial_capital=self.initial_capital,
            final_capital=final_equity,
            performance_metrics=performance_metrics,
            trades=self.trades,
            equity_curve=self.equity_curve,
            indicators={}  # Will be populated by strategy
        )
    
    def _open_trade(self, side: str, timestamp: datetime, price: float) -> Trade:
        """Open a new trade"""
        # Apply slippage
        execution_price = price * (1 + self.slippage) if side == "buy" else price * (1 - self.slippage)
        
        # Calculate position size (use all available cash)
        if side == "buy":
            # Long position
            gross_quantity = int(self.cash / execution_price)
            if gross_quantity > 0:
                commission_cost = gross_quantity * execution_price * self.commission
                net_cost = gross_quantity * execution_price + commission_cost
                
                if net_cost <= self.cash:
                    self.position = gross_quantity
                    self.cash -= net_cost
                else:
                    # Adjust for commission
                    adjusted_quantity = int(self.cash / (execution_price * (1 + self.commission)))
                    if adjusted_quantity > 0:
                        self.position = adjusted_quantity
                        net_cost = adjusted_quantity * execution_price * (1 + self.commission)
                        self.cash -= net_cost
                        gross_quantity = adjusted_quantity
        else:
            # Short position (simplified - assume we can short)
            gross_quantity = int(self.cash / execution_price)
            if gross_quantity > 0:
                commission_cost = gross_quantity * execution_price * self.commission
                self.position = -gross_quantity
                self.cash += gross_quantity * execution_price - commission_cost
        
        trade = Trade(
            entry_date=timestamp,
            exit_date=None,
            entry_price=execution_price,
            exit_price=None,
            quantity=abs(gross_quantity) if 'gross_quantity' in locals() else 0,
            side=side,
            pnl=None,
            pnl_pct=None
        )
        
        return trade
    
    def _close_trade(self, trade: Trade, timestamp: datetime, price: float):
        """Close an existing trade"""
        # Apply slippage
        execution_price = price * (1 - self.slippage) if trade.side == "buy" else price * (1 + self.slippage)
        
        # Calculate P&L
        if trade.side == "buy":
            # Closing long position
            gross_proceeds = self.position * execution_price
            commission_cost = gross_proceeds * self.commission
            net_proceeds = gross_proceeds - commission_cost
            self.cash += net_proceeds
            
            pnl = (execution_price - trade.entry_price) * trade.quantity - (trade.entry_price * trade.quantity * self.commission) - commission_cost
        else:
            # Closing short position
            cost = abs(self.position) * execution_price
            commission_cost = cost * self.commission
            total_cost = cost + commission_cost
            self.cash -= total_cost
            
            pnl = (trade.entry_price - execution_price) * trade.quantity - (trade.entry_price * trade.quantity * self.commission) - commission_cost
        
        # Update trade
        trade.exit_date = timestamp
        trade.exit_price = execution_price
        trade.pnl = pnl
        trade.pnl_pct = (pnl / (trade.entry_price * trade.quantity)) * 100 if trade.quantity > 0 else 0
        
        # Reset position
        self.position = 0
        
        # Add to completed trades
        self.trades.append(trade)
