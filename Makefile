# QuantDojo Docker Management
.PHONY: help build up down logs clean test lint format

# Default target
help:
	@echo "QuantDojo Docker Management Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev-up          - Start development environment"
	@echo "  make dev-down        - Stop development environment"
	@echo "  make dev-logs        - View development logs"
	@echo "  make dev-build       - Build development images"
	@echo ""
	@echo "Production:"
	@echo "  make prod-up         - Start production environment"
	@echo "  make prod-down       - Stop production environment"
	@echo "  make prod-logs       - View production logs"
	@echo "  make prod-build      - Build production images"
	@echo ""
	@echo "Database:"
	@echo "  make db-migrate      - Run database migrations"
	@echo "  make db-reset        - Reset database (WARNING: destroys data)"
	@echo "  make db-backup       - Backup database"
	@echo "  make db-restore      - Restore database from backup"
	@echo ""
	@echo "Maintenance:"
	@echo "  make clean           - Clean up containers and images"
	@echo "  make test            - Run test suite"
	@echo "  make lint            - Run code linting"
	@echo "  make format          - Format code"

# Development commands
dev-up:
	@echo "Starting QuantDojo development environment..."
	docker-compose --env-file .env.dev up -d
	@echo "Development environment started!"
	@echo "Frontend: http://localhost:3000"
	@echo "Backend API: http://localhost:8000"
	@echo "API Docs: http://localhost:8000/docs"

dev-down:
	@echo "Stopping development environment..."
	docker-compose --env-file .env.dev down

dev-logs:
	docker-compose --env-file .env.dev logs -f

dev-build:
	@echo "Building development images..."
	docker-compose --env-file .env.dev build

dev-restart:
	@echo "Restarting development environment..."
	docker-compose --env-file .env.dev restart

# Production commands
prod-up:
	@echo "Starting QuantDojo production environment..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "Production environment started!"

prod-down:
	@echo "Stopping production environment..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

prod-logs:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f

prod-build:
	@echo "Building production images..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Database commands
db-migrate:
	@echo "Running database migrations..."
	docker-compose --env-file .env.dev exec backend alembic upgrade head

db-reset:
	@echo "WARNING: This will destroy all data!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose --env-file .env.dev down -v; \
		docker-compose --env-file .env.dev up -d postgres redis; \
		sleep 10; \
		docker-compose --env-file .env.dev exec backend alembic upgrade head; \
	fi

db-backup:
	@echo "Creating database backup..."
	docker-compose --env-file .env.dev exec postgres pg_dump -U quantdojo quantdojo > backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore:
	@echo "Restoring database from backup..."
	@read -p "Enter backup file name: " backup_file; \
	docker-compose --env-file .env.dev exec -T postgres psql -U quantdojo quantdojo < $$backup_file

# Testing commands
test:
	@echo "Running test suite..."
	docker-compose --env-file .env.dev exec backend pytest -v --cov=app --cov-report=html

test-frontend:
	@echo "Running frontend tests..."
	docker-compose --env-file .env.dev exec frontend npm test

# Code quality commands
lint:
	@echo "Running backend linting..."
	docker-compose --env-file .env.dev exec backend flake8 app/
	docker-compose --env-file .env.dev exec backend mypy app/
	@echo "Running frontend linting..."
	docker-compose --env-file .env.dev exec frontend npm run lint

format:
	@echo "Formatting backend code..."
	docker-compose --env-file .env.dev exec backend black app/
	docker-compose --env-file .env.dev exec backend isort app/
	@echo "Formatting frontend code..."
	docker-compose --env-file .env.dev exec frontend npm run format

# Maintenance commands
clean:
	@echo "Cleaning up Docker resources..."
	docker-compose --env-file .env.dev down -v --remove-orphans
	docker system prune -f
	docker volume prune -f

clean-all:
	@echo "WARNING: This will remove ALL Docker resources!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose --env-file .env.dev down -v --remove-orphans; \
		docker system prune -af; \
		docker volume prune -f; \
	fi

# Health checks
health:
	@echo "Checking service health..."
	@echo "Backend health:"
	@curl -f http://localhost:8000/health || echo "Backend unhealthy"
	@echo ""
	@echo "Frontend health:"
	@curl -f http://localhost:3000/health || echo "Frontend unhealthy"

# Monitoring
monitor:
	@echo "Opening monitoring dashboards..."
	@echo "Grafana: http://localhost:3001 (admin/admin)"
	@echo "Prometheus: http://localhost:9090"

# Quick setup for new developers
setup:
	@echo "Setting up QuantDojo development environment..."
	@if [ ! -f .env.dev ]; then \
		echo "Creating .env.dev from example..."; \
		cp .env.example .env.dev; \
	fi
	@echo "Building and starting services..."
	make dev-build
	make dev-up
	@echo "Waiting for services to be ready..."
	sleep 30
	make db-migrate
	@echo ""
	@echo "Setup complete! QuantDojo is ready for development."
	@echo "Frontend: http://localhost:3000"
	@echo "Backend API: http://localhost:8000/docs"
