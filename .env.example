# Environment Configuration
ENVIRONMENT=development

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=******************************************************************/quantdojo

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password
REDIS_URL=redis://:your_secure_redis_password@redis:6379/0

# Application Security
SECRET_KEY=your_super_secret_key_min_32_characters_long
JWT_SECRET_KEY=your_jwt_secret_key_different_from_above
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# External Services
SENTRY_DSN=https://<EMAIL>/project-id

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development

# Build Configuration
BUILD_TARGET=development

# Monitoring (Production)
GRAFANA_PASSWORD=your_grafana_admin_password

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload Limits
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=csv,xlsx,xls

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Data Provider API Keys (optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
IEX_CLOUD_API_KEY=your_iex_cloud_key
POLYGON_API_KEY=your_polygon_key
