"""
Configuration settings for QuantDojo
"""

import os
from pydantic_settings import BaseSettings
from typing import List, Optional

class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "QuantDojo"
    VERSION: str = "1.0.0"

    # Database Settings
    DATABASE_URL: str = "postgresql+psycopg2://quantdojo:quantdojo123@localhost:5432/quantdojo"
    ASYNC_DATABASE_URL: str = "postgresql+asyncpg://quantdojo:quantdojo123@localhost:5432/quantdojo"

    # Security Settings
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"

    # Redis Settings (for caching and background jobs)
    REDIS_URL: str = "redis://localhost:6379/0"

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8080",
    ]
    
    # Data Settings
    DEFAULT_PERIOD: str = "1y"
    DEFAULT_INTERVAL: str = "1d"
    MAX_DATA_POINTS: int = 10000
    
    # Backtesting Settings
    DEFAULT_INITIAL_CAPITAL: float = 10000.0
    DEFAULT_COMMISSION: float = 0.001
    DEFAULT_SLIPPAGE: float = 0.001
    MIN_CAPITAL: float = 1000.0
    MAX_CAPITAL: float = 10000000.0
    
    # Cache Settings
    CACHE_TTL: int = 300  # 5 minutes

    # Database Connection Pool Settings
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600  # 1 hour
    DB_POOL_PRE_PING: bool = True

    # Query Performance Settings
    DB_QUERY_TIMEOUT: int = 30
    DB_SLOW_QUERY_THRESHOLD: float = 1.0  # seconds
    DB_ENABLE_QUERY_LOGGING: bool = True
    DB_LOG_SLOW_QUERIES: bool = True

    # Production Database Optimization
    DB_STATEMENT_TIMEOUT: int = 60000  # 60 seconds in milliseconds
    DB_LOCK_TIMEOUT: int = 30000  # 30 seconds in milliseconds
    DB_IDLE_IN_TRANSACTION_TIMEOUT: int = 300000  # 5 minutes in milliseconds
    
    @property
    def database_url(self) -> str:
        """Get the synchronous database URL"""
        return self.DATABASE_URL

    @property
    def async_database_url(self) -> str:
        """Get the asynchronous database URL"""
        return self.ASYNC_DATABASE_URL

    class Config:
        env_file = ".env"
        case_sensitive = True


def get_settings() -> Settings:
    """Get application settings"""
    return Settings()


settings = get_settings()
