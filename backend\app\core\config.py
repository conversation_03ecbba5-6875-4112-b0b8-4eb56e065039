"""
Configuration settings for QuantDojo
"""

from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "QuantDojo"
    VERSION: str = "1.0.0"
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8080",
    ]
    
    # Data Settings
    DEFAULT_PERIOD: str = "1y"
    DEFAULT_INTERVAL: str = "1d"
    MAX_DATA_POINTS: int = 10000
    
    # Backtesting Settings
    DEFAULT_INITIAL_CAPITAL: float = 10000.0
    DEFAULT_COMMISSION: float = 0.001
    DEFAULT_SLIPPAGE: float = 0.001
    MIN_CAPITAL: float = 1000.0
    MAX_CAPITAL: float = 10000000.0
    
    # Cache Settings
    CACHE_TTL: int = 300  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
