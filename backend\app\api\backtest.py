"""
Backtesting API endpoints
"""

from fastapi import APIRouter, HTTPException
from typing import Union
import yfinance as yf
from datetime import datetime, timedelta

from app.core.models import (
    BacktestRequest, BacktestResult, StrategyType, 
    MovingAverageParams, RSIParams, OHLCVData
)
from app.strategies.moving_average import MovingAverageStrategy
from app.strategies.rsi import RSIStrategy
from app.utils.backtester import BacktestEngine

router = APIRouter()

# Strategy registry
STRATEGY_REGISTRY = {
    StrategyType.MOVING_AVERAGE: MovingAverageStrategy,
    StrategyType.RSI: RSIStrategy
}

@router.post("/")
async def run_backtest(request: BacktestRequest) -> BacktestResult:
    """
    Execute a backtest for the given strategy and parameters
    """
    try:
        # Fetch market data
        data = await _fetch_market_data(
            request.symbol,
            request.start_date,
            request.end_date
        )
        
        if not data:
            raise HTTPException(
                status_code=404,
                detail=f"No data found for symbol {request.symbol}"
            )
        
        # Get strategy class
        if request.strategy not in STRATEGY_REGISTRY:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown strategy: {request.strategy}"
            )
        
        strategy_class = STRATEGY_REGISTRY[request.strategy]
        
        # Convert parameters to dict
        if isinstance(request.parameters, MovingAverageParams):
            params_dict = {
                'fast_period': request.parameters.fast_period,
                'slow_period': request.parameters.slow_period,
                'ma_type': request.parameters.ma_type.value
            }
        elif isinstance(request.parameters, RSIParams):
            params_dict = {
                'period': request.parameters.period,
                'overbought': request.parameters.overbought,
                'oversold': request.parameters.oversold
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid parameters for strategy"
            )
        
        # Add symbol to parameters for result tracking
        params_dict['symbol'] = request.symbol
        
        # Initialize strategy
        strategy = strategy_class(params_dict)
        
        # Generate signals and indicators
        signals, indicators = strategy.generate_signals(data)
        
        # Initialize backtesting engine
        engine = BacktestEngine(
            data=data,
            initial_capital=request.initial_capital,
            commission=request.commission,
            slippage=request.slippage
        )
        
        # Execute backtest
        result = engine.execute_backtest(
            signals=signals,
            strategy_name=request.strategy.value,
            parameters=params_dict
        )
        
        # Add indicators to result
        result.indicators = indicators
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Backtest execution failed: {str(e)}"
        )

async def _fetch_market_data(
    symbol: str,
    start_date: str = None,
    end_date: str = None
) -> list[OHLCVData]:
    """
    Fetch market data for backtesting
    """
    try:
        ticker = yf.Ticker(symbol.upper())
        
        # Set default date range if not provided
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        # Fetch historical data
        hist = ticker.history(start=start_date, end=end_date)
        
        if hist.empty:
            return []
        
        # Convert to OHLCVData objects
        data = []
        for timestamp, row in hist.iterrows():
            data.append(OHLCVData(
                timestamp=timestamp.to_pydatetime(),
                open=float(row['Open']),
                high=float(row['High']),
                low=float(row['Low']),
                close=float(row['Close']),
                volume=int(row['Volume'])
            ))
        
        return data
        
    except Exception as e:
        raise Exception(f"Failed to fetch data for {symbol}: {str(e)}")

@router.get("/validate-parameters/{strategy_name}")
async def validate_strategy_parameters(
    strategy_name: str,
    parameters: dict
):
    """
    Validate strategy parameters without running backtest
    """
    try:
        strategy_type = StrategyType(strategy_name)
        
        if strategy_type not in STRATEGY_REGISTRY:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown strategy: {strategy_name}"
            )
        
        strategy_class = STRATEGY_REGISTRY[strategy_type]
        
        # Try to initialize strategy with parameters
        strategy = strategy_class(parameters)
        
        return {
            "valid": True,
            "message": "Parameters are valid",
            "schema": strategy.get_parameter_schema()
        }
        
    except ValueError as e:
        return {
            "valid": False,
            "message": str(e),
            "schema": None
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Parameter validation failed: {str(e)}"
        )
