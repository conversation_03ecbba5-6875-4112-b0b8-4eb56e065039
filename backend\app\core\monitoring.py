"""
Monitoring and metrics collection for QuantDojo
"""

import asyncio
import signal
import time
from typing import Dict, Any, Optional
import psutil
import os

from app.core.logging import get_logger

logger = get_logger("quantdojo.monitoring")


class ApplicationMetrics:
    """Application metrics collector"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        self.backtest_count = 0
        self.active_connections = 0
        
    def increment_requests(self):
        """Increment request counter"""
        self.request_count += 1
    
    def increment_errors(self):
        """Increment error counter"""
        self.error_count += 1
    
    def increment_backtests(self):
        """Increment backtest counter"""
        self.backtest_count += 1
    
    def set_active_connections(self, count: int):
        """Set active connections count"""
        self.active_connections = count
    
    def get_uptime(self) -> float:
        """Get application uptime in seconds"""
        return time.time() - self.start_time
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all metrics"""
        process = psutil.Process()
        
        return {
            "application": {
                "uptime_seconds": self.get_uptime(),
                "request_count": self.request_count,
                "error_count": self.error_count,
                "backtest_count": self.backtest_count,
                "active_connections": self.active_connections,
                "error_rate": self.error_count / max(self.request_count, 1),
            },
            "system": {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024,
                "disk_percent": psutil.disk_usage('/').percent,
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0],
            },
            "process": {
                "pid": process.pid,
                "cpu_percent": process.cpu_percent(),
                "memory_percent": process.memory_percent(),
                "memory_rss_mb": process.memory_info().rss / 1024 / 1024,
                "num_threads": process.num_threads(),
                "num_fds": process.num_fds() if hasattr(process, 'num_fds') else 0,
                "create_time": process.create_time(),
            }
        }


# Global metrics instance
metrics = ApplicationMetrics()


class GracefulShutdown:
    """Handles graceful shutdown of the application"""
    
    def __init__(self):
        self.shutdown_event = asyncio.Event()
        self.tasks: set = set()
        self.cleanup_callbacks = []
        
    def add_cleanup_callback(self, callback):
        """Add a cleanup callback to be called during shutdown"""
        self.cleanup_callbacks.append(callback)
    
    def add_task(self, task):
        """Add a task to be cancelled during shutdown"""
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
    
    async def shutdown(self):
        """Perform graceful shutdown"""
        logger.info("Starting graceful shutdown...")
        
        # Set shutdown event
        self.shutdown_event.set()
        
        # Cancel all running tasks
        logger.info(f"Cancelling {len(self.tasks)} running tasks...")
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete with timeout
        if self.tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.tasks, return_exceptions=True),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout")
        
        # Run cleanup callbacks
        logger.info(f"Running {len(self.cleanup_callbacks)} cleanup callbacks...")
        for callback in self.cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")
        
        logger.info("Graceful shutdown completed")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        # Windows doesn't have SIGHUP
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)


# Global shutdown handler
shutdown_handler = GracefulShutdown()


class HealthChecker:
    """Health check utilities"""
    
    @staticmethod
    async def check_database(db_session) -> Dict[str, Any]:
        """Check database health"""
        try:
            from sqlalchemy import text
            start_time = time.time()
            await db_session.execute(text("SELECT 1"))
            response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    @staticmethod
    async def check_redis(redis_url: str) -> Dict[str, Any]:
        """Check Redis health"""
        try:
            import redis.asyncio as redis
            start_time = time.time()
            
            redis_client = redis.from_url(redis_url)
            await redis_client.ping()
            response_time = (time.time() - start_time) * 1000
            await redis_client.close()
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    @staticmethod
    async def check_external_service(url: str, timeout: float = 5.0) -> Dict[str, Any]:
        """Check external service health"""
        try:
            import httpx
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy" if response.status_code == 200 else "degraded",
                "response_time_ms": round(response_time, 2),
                "status_code": response.status_code
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
