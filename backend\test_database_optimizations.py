#!/usr/bin/env python3
"""
Comprehensive test suite for database optimizations and performance enhancements
Tests connection pooling, query performance monitoring, and indexing strategies
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import get_settings
from app.core.database import (
    get_async_session, get_connection_pool_stats, 
    health_check_database, optimize_connection_pool
)
from app.core.database_performance import (
    get_performance_metrics, reset_performance_metrics,
    DatabaseOptimizer, performance_tracker
)
from app.core.logging import get_logger

logger = get_logger("quantdojo.test.database_optimizations")
settings = get_settings()


class DatabaseOptimizationTester:
    """Comprehensive database optimization testing suite"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "details": details
        })
        
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
    
    async def test_configuration_settings(self) -> bool:
        """Test database configuration settings"""
        try:
            # Test that all required settings are present
            required_settings = [
                'DB_POOL_SIZE', 'DB_MAX_OVERFLOW', 'DB_POOL_TIMEOUT',
                'DB_POOL_RECYCLE', 'DB_SLOW_QUERY_THRESHOLD',
                'DB_ENABLE_QUERY_LOGGING', 'DB_STATEMENT_TIMEOUT'
            ]
            
            missing_settings = []
            for setting in required_settings:
                if not hasattr(settings, setting):
                    missing_settings.append(setting)
            
            if missing_settings:
                self.log_test_result(
                    "Configuration Settings",
                    False,
                    f"Missing settings: {', '.join(missing_settings)}"
                )
                return False
            
            # Test setting values are reasonable
            if settings.DB_POOL_SIZE < 5 or settings.DB_POOL_SIZE > 100:
                self.log_test_result(
                    "Configuration Settings",
                    False,
                    f"DB_POOL_SIZE ({settings.DB_POOL_SIZE}) should be between 5-100"
                )
                return False
            
            if settings.DB_SLOW_QUERY_THRESHOLD <= 0:
                self.log_test_result(
                    "Configuration Settings",
                    False,
                    f"DB_SLOW_QUERY_THRESHOLD ({settings.DB_SLOW_QUERY_THRESHOLD}) should be > 0"
                )
                return False
            
            self.log_test_result(
                "Configuration Settings",
                True,
                f"Pool size: {settings.DB_POOL_SIZE}, Slow query threshold: {settings.DB_SLOW_QUERY_THRESHOLD}s"
            )
            return True
            
        except Exception as e:
            self.log_test_result("Configuration Settings", False, str(e))
            return False
    
    async def test_connection_pool_functionality(self) -> bool:
        """Test connection pool functionality"""
        try:
            # Get connection pool stats
            pool_stats = await get_connection_pool_stats()
            
            if "error" in pool_stats:
                self.log_test_result(
                    "Connection Pool Functionality",
                    False,
                    f"Error getting pool stats: {pool_stats['error']}"
                )
                return False
            
            # Verify pool configuration
            expected_pool_size = settings.DB_POOL_SIZE
            actual_pool_size = pool_stats.get("pool_size", 0)
            
            if actual_pool_size != expected_pool_size:
                self.log_test_result(
                    "Connection Pool Functionality",
                    False,
                    f"Pool size mismatch: expected {expected_pool_size}, got {actual_pool_size}"
                )
                return False
            
            # Test multiple concurrent connections
            async def test_connection():
                async for session in get_async_session():
                    await asyncio.sleep(0.1)  # Hold connection briefly
                    break
            
            # Create multiple concurrent connections
            tasks = [test_connection() for _ in range(5)]
            await asyncio.gather(*tasks)
            
            self.log_test_result(
                "Connection Pool Functionality",
                True,
                f"Pool size: {actual_pool_size}, Available: {pool_stats.get('available_connections', 0)}"
            )
            return True
            
        except Exception as e:
            self.log_test_result("Connection Pool Functionality", False, str(e))
            return False
    
    async def test_performance_monitoring(self) -> bool:
        """Test performance monitoring functionality"""
        try:
            # Reset metrics first
            await reset_performance_metrics()

            # Perform some database operations to generate metrics
            async for session in get_async_session():
                # Simulate some queries (SQLite compatible)
                from sqlalchemy import text
                await session.execute(text("SELECT 1"))
                await session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))

                break

            # Get performance metrics
            metrics = await get_performance_metrics()

            # Verify metrics structure
            required_keys = ["timestamp", "connection_stats", "query_stats"]
            missing_keys = [key for key in required_keys if key not in metrics]

            if missing_keys:
                self.log_test_result(
                    "Performance Monitoring",
                    False,
                    f"Missing metric keys: {', '.join(missing_keys)}"
                )
                return False

            # Verify query stats
            query_stats = metrics.get("query_stats", {})
            if query_stats.get("total_queries", 0) < 1:
                self.log_test_result(
                    "Performance Monitoring",
                    False,
                    f"Expected at least 1 query, got {query_stats.get('total_queries', 0)}"
                )
                return False

            self.log_test_result(
                "Performance Monitoring",
                True,
                f"Tracked {query_stats.get('total_queries', 0)} queries, {query_stats.get('slow_queries_count', 0)} slow"
            )
            return True

        except Exception as e:
            self.log_test_result("Performance Monitoring", False, str(e))
            return False
    
    async def test_database_health_check(self) -> bool:
        """Test database health check functionality"""
        try:
            health_status = await health_check_database()
            
            if health_status.get("status") not in ["healthy", "warning"]:
                self.log_test_result(
                    "Database Health Check",
                    False,
                    f"Unhealthy database status: {health_status.get('status')}"
                )
                return False
            
            # Verify health check components
            checks = health_status.get("checks", {})
            required_checks = ["connectivity", "connection_pool"]
            
            missing_checks = [check for check in required_checks if check not in checks]
            if missing_checks:
                self.log_test_result(
                    "Database Health Check",
                    False,
                    f"Missing health checks: {', '.join(missing_checks)}"
                )
                return False
            
            self.log_test_result(
                "Database Health Check",
                True,
                f"Status: {health_status.get('status')}, Checks: {len(checks)}"
            )
            return True
            
        except Exception as e:
            self.log_test_result("Database Health Check", False, str(e))
            return False
    
    async def test_query_optimization_features(self) -> bool:
        """Test query optimization and analysis features"""
        try:
            # For SQLite, we'll test basic functionality without PostgreSQL-specific features
            async for session in get_async_session():
                # Test basic database operations
                from sqlalchemy import text
                result = await session.execute(text("SELECT 1 as test_value"))
                row = result.first()

                if not row or row.test_value != 1:
                    self.log_test_result(
                        "Query Optimization Features",
                        False,
                        "Basic query test failed"
                    )
                    return False

                break

            self.log_test_result(
                "Query Optimization Features",
                True,
                "Basic database operations working correctly"
            )
            return True

        except Exception as e:
            self.log_test_result("Query Optimization Features", False, str(e))
            return False
    
    async def test_connection_pool_optimization(self) -> bool:
        """Test connection pool optimization"""
        try:
            optimization_result = await optimize_connection_pool()
            
            if "error" in optimization_result:
                self.log_test_result(
                    "Connection Pool Optimization",
                    False,
                    f"Optimization error: {optimization_result['error']}"
                )
                return False
            
            # Verify optimization provides useful metrics
            required_keys = ["pool_size", "checked_out", "overflow"]
            missing_keys = [key for key in required_keys if key not in optimization_result]
            
            if missing_keys:
                self.log_test_result(
                    "Connection Pool Optimization",
                    False,
                    f"Missing optimization keys: {', '.join(missing_keys)}"
                )
                return False
            
            utilization = (optimization_result["checked_out"] / optimization_result["pool_size"]) * 100
            
            self.log_test_result(
                "Connection Pool Optimization",
                True,
                f"Pool utilization: {utilization:.1f}%"
            )
            return True
            
        except Exception as e:
            self.log_test_result("Connection Pool Optimization", False, str(e))
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all database optimization tests"""
        print("🧪 Running Database Optimization Tests...")
        print("=" * 50)
        
        # Run all tests
        tests = [
            self.test_configuration_settings(),
            self.test_connection_pool_functionality(),
            self.test_performance_monitoring(),
            self.test_database_health_check(),
            self.test_query_optimization_features(),
            self.test_connection_pool_optimization()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Handle any exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.log_test_result(f"Test {i+1}", False, str(result))
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {self.passed_tests} passed, {self.failed_tests} failed")
        
        success_rate = (self.passed_tests / (self.passed_tests + self.failed_tests)) * 100
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        return {
            "total_tests": self.passed_tests + self.failed_tests,
            "passed": self.passed_tests,
            "failed": self.failed_tests,
            "success_rate": success_rate,
            "results": self.test_results
        }


async def main():
    """Main test runner"""
    print("🚀 QuantDojo Database Optimization Test Suite")
    print(f"Environment: {settings.ENVIRONMENT}")
    print(f"Debug Mode: {settings.DEBUG}")
    print("-" * 50)
    
    tester = DatabaseOptimizationTester()
    
    try:
        results = await tester.run_all_tests()
        
        if results["success_rate"] >= 80:
            print("\n🎉 Database optimizations are working correctly!")
            return 0
        else:
            print(f"\n⚠️  Some tests failed. Success rate: {results['success_rate']:.1f}%")
            return 1
            
    except Exception as e:
        print(f"\n❌ Fatal error during testing: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
