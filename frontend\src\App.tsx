import React, { useEffect } from 'react';
import { useBacktestStore } from './store/backtestStore';
import { TradingChart } from './components/Charts/TradingChart';
import { EquityCurve } from './components/Charts/EquityCurve';
import { StrategySelector } from './components/Controls/StrategySelector';
import { ParameterControls } from './components/Controls/ParameterControls';
import { MetricsPanel } from './components/Dashboard/MetricsPanel';
import { PerformanceTable } from './components/Dashboard/PerformanceTable';
import { FileUpload } from './components/Controls/FileUpload';
import { Loader2, TrendingUp, BarChart3, Settings, Upload } from 'lucide-react';
import toast from 'react-hot-toast';

function App() {
  const {
    isLoading,
    error,
    symbol,
    marketData,
    backtestResult,
    setSymbol,
    fetchMarketData,
    fetchStrategies,
    runBacktest,
    setError
  } = useBacktestStore();

  // Initialize app
  useEffect(() => {
    fetchStrategies();
    fetchMarketData('AAPL');
  }, [fetchStrategies, fetchMarketData]);

  // Handle errors with toast notifications
  useEffect(() => {
    if (error) {
      toast.error(error);
      setError(null);
    }
  }, [error, setError]);

  const handleSymbolSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const newSymbol = formData.get('symbol') as string;
    if (newSymbol && newSymbol.trim()) {
      setSymbol(newSymbol.trim());
      fetchMarketData(newSymbol.trim());
    }
  };

  const handleRunBacktest = async () => {
    if (!marketData) {
      toast.error('Please load market data first');
      return;
    }
    
    try {
      await runBacktest();
      toast.success('Backtest completed successfully!');
    } catch (error) {
      // Error is handled by the store and useEffect above
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <TrendingUp className="h-8 w-8 text-primary-600" />
              <h1 className="text-2xl font-bold text-gray-900">QuantDojo</h1>
              <span className="text-sm text-gray-500 hidden sm:inline">
                Interactive Trading Strategy Backtester
              </span>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Symbol Input */}
              <form onSubmit={handleSymbolSubmit} className="flex items-center space-x-2">
                <input
                  name="symbol"
                  type="text"
                  placeholder="Enter symbol (e.g., AAPL)"
                  defaultValue={symbol}
                  className="input w-40"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn-primary btn-sm"
                >
                  Load Data
                </button>
              </form>
              
              {/* Run Backtest Button */}
              <button
                onClick={handleRunBacktest}
                disabled={isLoading || !marketData}
                className="btn-success btn-md flex items-center space-x-2"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <BarChart3 className="h-4 w-4" />
                )}
                <span>Run Backtest</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Controls */}
          <div className="lg:col-span-1 space-y-6">
            {/* Strategy Selection */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-gray-600" />
                  <h3 className="card-title">Strategy Configuration</h3>
                </div>
              </div>
              <div className="card-content space-y-4">
                <StrategySelector />
                <ParameterControls />
              </div>
            </div>

            {/* File Upload */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center space-x-2">
                  <Upload className="h-5 w-5 text-gray-600" />
                  <h3 className="card-title">Custom Data</h3>
                </div>
                <p className="card-description">
                  Upload your own CSV data file
                </p>
              </div>
              <div className="card-content">
                <FileUpload />
              </div>
            </div>

            {/* Performance Metrics */}
            {backtestResult && (
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Performance Metrics</h3>
                </div>
                <div className="card-content">
                  <MetricsPanel />
                </div>
              </div>
            )}
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Charts */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Price Chart */}
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">
                    {marketData ? `${marketData.symbol} Price Chart` : 'Price Chart'}
                  </h3>
                  <p className="card-description">
                    OHLC data with strategy indicators and trade signals
                  </p>
                </div>
                <div className="card-content">
                  <div className="h-96">
                    <TradingChart />
                  </div>
                </div>
              </div>

              {/* Equity Curve */}
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Equity Curve</h3>
                  <p className="card-description">
                    Portfolio value over time
                  </p>
                </div>
                <div className="card-content">
                  <div className="h-96">
                    <EquityCurve />
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Table */}
            {backtestResult && (
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Trade History</h3>
                  <p className="card-description">
                    Detailed breakdown of all trades
                  </p>
                </div>
                <div className="card-content">
                  <PerformanceTable />
                </div>
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="card">
                <div className="card-content">
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary-600" />
                      <p className="text-gray-600">
                        {!marketData ? 'Loading market data...' : 'Running backtest...'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !marketData && (
              <div className="card">
                <div className="card-content">
                  <div className="text-center py-12">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Welcome to QuantDojo
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Enter a stock symbol above to get started with backtesting
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
