"""
Performance metrics calculation utilities
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any
from app.core.models import PerformanceMetrics, Trade

def calculate_performance_metrics(
    trades: List[Trade],
    equity_curve: List[Dict[str, Any]],
    initial_capital: float,
    risk_free_rate: float = 0.02
) -> PerformanceMetrics:
    """
    Calculate comprehensive performance metrics from trades and equity curve
    """
    if not trades or not equity_curve:
        return PerformanceMetrics(
            total_return=0.0,
            total_return_pct=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            max_drawdown_pct=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            avg_trade=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            largest_win=0.0,
            largest_loss=0.0
        )
    
    # Extract equity values
    equity_values = [point['equity'] for point in equity_curve]
    final_capital = equity_values[-1]
    
    # Basic return metrics
    total_return = final_capital - initial_capital
    total_return_pct = (total_return / initial_capital) * 100
    
    # Calculate returns for Sharpe ratio
    returns = np.diff(equity_values) / equity_values[:-1]
    
    # Sharpe ratio calculation
    if len(returns) > 1 and np.std(returns) > 0:
        excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    else:
        sharpe_ratio = 0.0
    
    # Maximum drawdown calculation
    peak = equity_values[0]
    max_drawdown = 0.0
    max_drawdown_pct = 0.0
    
    for equity in equity_values:
        if equity > peak:
            peak = equity
        drawdown = peak - equity
        drawdown_pct = (drawdown / peak) * 100 if peak > 0 else 0
        
        if drawdown > max_drawdown:
            max_drawdown = drawdown
        if drawdown_pct > max_drawdown_pct:
            max_drawdown_pct = drawdown_pct
    
    # Trade analysis
    completed_trades = [trade for trade in trades if trade.pnl is not None]
    total_trades = len(completed_trades)
    
    if total_trades == 0:
        return PerformanceMetrics(
            total_return=total_return,
            total_return_pct=total_return_pct,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_pct=max_drawdown_pct,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            avg_trade=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            largest_win=0.0,
            largest_loss=0.0
        )
    
    # Separate winning and losing trades
    winning_trades = [trade for trade in completed_trades if trade.pnl > 0]
    losing_trades = [trade for trade in completed_trades if trade.pnl < 0]
    
    winning_count = len(winning_trades)
    losing_count = len(losing_trades)
    
    # Win rate
    win_rate = (winning_count / total_trades) * 100 if total_trades > 0 else 0
    
    # PnL calculations
    total_pnl = sum(trade.pnl for trade in completed_trades)
    avg_trade = total_pnl / total_trades if total_trades > 0 else 0
    
    # Winning trade metrics
    if winning_trades:
        gross_profit = sum(trade.pnl for trade in winning_trades)
        avg_win = gross_profit / winning_count
        largest_win = max(trade.pnl for trade in winning_trades)
    else:
        gross_profit = 0
        avg_win = 0
        largest_win = 0
    
    # Losing trade metrics
    if losing_trades:
        gross_loss = abs(sum(trade.pnl for trade in losing_trades))
        avg_loss = gross_loss / losing_count
        largest_loss = abs(min(trade.pnl for trade in losing_trades))
    else:
        gross_loss = 0
        avg_loss = 0
        largest_loss = 0
    
    # Profit factor
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0
    
    return PerformanceMetrics(
        total_return=total_return,
        total_return_pct=total_return_pct,
        sharpe_ratio=sharpe_ratio,
        max_drawdown=max_drawdown,
        max_drawdown_pct=max_drawdown_pct,
        win_rate=win_rate,
        profit_factor=profit_factor,
        total_trades=total_trades,
        winning_trades=winning_count,
        losing_trades=losing_count,
        avg_trade=avg_trade,
        avg_win=avg_win,
        avg_loss=avg_loss,
        largest_win=largest_win,
        largest_loss=largest_loss
    )
