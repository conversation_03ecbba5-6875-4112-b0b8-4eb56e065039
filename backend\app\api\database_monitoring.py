"""
Database monitoring and performance API endpoints
Provides real-time database performance metrics and optimization insights
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
import asyncio
from datetime import datetime, timedelta

from app.core.database import get_async_session, get_connection_pool_stats, health_check_database
from app.core.database_performance import (
    get_performance_metrics, 
    reset_performance_metrics,
    DatabaseOptimizer
)
from app.core.auth import get_current_superuser
from app.models.user import User
from app.core.logging import get_logger

logger = get_logger("quantdojo.api.database_monitoring")
router = APIRouter()


@router.get("/performance/metrics")
async def get_database_performance_metrics(
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Get comprehensive database performance metrics
    Requires superuser privileges
    """
    try:
        metrics = await get_performance_metrics()
        logger.info("Database performance metrics retrieved", user_id=current_user.id)
        return {
            "status": "success",
            "data": metrics
        }
    except Exception as e:
        logger.error("Error retrieving performance metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving performance metrics: {str(e)}"
        )


@router.get("/performance/connection-pool")
async def get_connection_pool_metrics(
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Get detailed connection pool statistics
    Requires superuser privileges
    """
    try:
        pool_stats = await get_connection_pool_stats()
        logger.info("Connection pool metrics retrieved", user_id=current_user.id)
        return {
            "status": "success",
            "data": pool_stats
        }
    except Exception as e:
        logger.error("Error retrieving connection pool metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving connection pool metrics: {str(e)}"
        )


@router.get("/health/detailed")
async def get_detailed_database_health(
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Get comprehensive database health check
    Requires superuser privileges
    """
    try:
        health_status = await health_check_database()
        logger.info("Database health check performed", 
                   user_id=current_user.id,
                   status=health_status["status"])
        return {
            "status": "success",
            "data": health_status
        }
    except Exception as e:
        logger.error("Error performing database health check", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing database health check: {str(e)}"
        )


@router.get("/performance/query-analysis")
async def get_query_performance_analysis(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Analyze database query performance using pg_stat_statements
    Requires superuser privileges
    """
    try:
        analysis = await DatabaseOptimizer.analyze_query_performance(db)
        logger.info("Query performance analysis completed", user_id=current_user.id)
        return {
            "status": "success",
            "data": analysis
        }
    except Exception as e:
        logger.error("Error analyzing query performance", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing query performance: {str(e)}"
        )


@router.get("/statistics/database")
async def get_database_statistics(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Get comprehensive database statistics
    Requires superuser privileges
    """
    try:
        stats = await DatabaseOptimizer.get_database_stats(db)
        logger.info("Database statistics retrieved", user_id=current_user.id)
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        logger.error("Error retrieving database statistics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving database statistics: {str(e)}"
        )


@router.post("/performance/reset-metrics")
async def reset_database_performance_metrics(
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Reset performance tracking metrics
    Requires superuser privileges
    """
    try:
        await reset_performance_metrics()
        logger.info("Database performance metrics reset", user_id=current_user.id)
        return {
            "status": "success",
            "message": "Performance metrics reset successfully"
        }
    except Exception as e:
        logger.error("Error resetting performance metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting performance metrics: {str(e)}"
        )


@router.get("/performance/recommendations")
async def get_performance_recommendations(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_superuser)
) -> Dict[str, Any]:
    """
    Get database performance optimization recommendations
    Requires superuser privileges
    """
    try:
        recommendations = []
        
        # Get current metrics
        pool_stats = await get_connection_pool_stats()
        performance_metrics = await get_performance_metrics()
        db_stats = await DatabaseOptimizer.get_database_stats(db)
        
        # Analyze connection pool utilization
        if "error" not in pool_stats:
            utilization = (pool_stats["checked_out"] / pool_stats["pool_size"]) * 100
            
            if utilization > 80:
                recommendations.append({
                    "category": "connection_pool",
                    "priority": "high",
                    "issue": "High connection pool utilization",
                    "recommendation": "Consider increasing DB_POOL_SIZE or DB_MAX_OVERFLOW",
                    "current_value": f"{utilization:.1f}% utilization",
                    "suggested_action": "Increase pool_size from current value or add more overflow connections"
                })
            elif utilization < 20:
                recommendations.append({
                    "category": "connection_pool",
                    "priority": "low",
                    "issue": "Low connection pool utilization",
                    "recommendation": "Pool size may be optimized for better resource usage",
                    "current_value": f"{utilization:.1f}% utilization",
                    "suggested_action": "Consider reducing pool_size to optimize memory usage"
                })
        
        # Analyze slow queries
        if "query_stats" in performance_metrics:
            slow_query_count = performance_metrics["query_stats"].get("slow_queries_count", 0)
            if slow_query_count > 10:
                recommendations.append({
                    "category": "query_performance",
                    "priority": "high",
                    "issue": f"High number of slow queries detected ({slow_query_count})",
                    "recommendation": "Review and optimize slow queries, consider adding indexes",
                    "current_value": f"{slow_query_count} slow queries",
                    "suggested_action": "Analyze query patterns and add appropriate indexes"
                })
        
        # Analyze database size and growth
        if "error" not in db_stats and "database" in db_stats:
            db_size_mb = db_stats["database"].get("size_mb", 0)
            if db_size_mb > 1000:  # 1GB
                recommendations.append({
                    "category": "storage",
                    "priority": "medium",
                    "issue": "Large database size detected",
                    "recommendation": "Consider implementing data archiving or partitioning strategy",
                    "current_value": f"{db_size_mb:.1f} MB",
                    "suggested_action": "Implement data retention policies and archiving for old market data"
                })
        
        # Check for dead tuples (PostgreSQL maintenance)
        if "error" not in db_stats and "tables" in db_stats:
            for table in db_stats["tables"]:
                dead_tuples = table.get("dead_tuples", 0)
                live_tuples = table.get("live_tuples", 1)
                if dead_tuples > 0 and (dead_tuples / live_tuples) > 0.1:  # More than 10% dead tuples
                    recommendations.append({
                        "category": "maintenance",
                        "priority": "medium",
                        "issue": f"High dead tuple ratio in table {table['table']}",
                        "recommendation": "Run VACUUM ANALYZE on the table",
                        "current_value": f"{dead_tuples} dead tuples out of {live_tuples} total",
                        "suggested_action": f"VACUUM ANALYZE {table['table']}"
                    })
        
        logger.info("Performance recommendations generated", 
                   user_id=current_user.id,
                   recommendation_count=len(recommendations))
        
        return {
            "status": "success",
            "data": {
                "recommendations": recommendations,
                "total_recommendations": len(recommendations),
                "high_priority": len([r for r in recommendations if r["priority"] == "high"]),
                "medium_priority": len([r for r in recommendations if r["priority"] == "medium"]),
                "low_priority": len([r for r in recommendations if r["priority"] == "low"]),
                "generated_at": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error("Error generating performance recommendations", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating performance recommendations: {str(e)}"
        )
