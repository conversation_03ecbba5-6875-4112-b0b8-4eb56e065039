"""
Security utilities and middleware for QuantDojo
Provides rate limiting, input validation, and secure file handling
"""

import os
import re
import hashlib
import mimetypes
from typing import List, Optional, Dict, Any
from pathlib import Path

from fastapi import Request, HTTPException, status, UploadFile
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import magic

from app.core.logging import get_logger
from app.core.errors import ValidationError, RateLimitError

logger = get_logger("quantdojo.security")

# Rate limiter configuration
limiter = Limiter(key_func=get_remote_address)


class SecurityConfig:
    """Security configuration constants"""
    
    # File upload limits
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB default
    ALLOWED_EXTENSIONS = {".py", ".txt", ".json", ".csv", ".xlsx"}
    ALLOWED_MIME_TYPES = {
        "text/plain",
        "text/x-python",
        "application/json",
        "text/csv",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel"
    }
    
    # Input validation
    MAX_STRING_LENGTH = 10000
    MAX_LIST_LENGTH = 1000
    
    # Rate limiting
    DEFAULT_RATE_LIMIT = os.getenv("DEFAULT_RATE_LIMIT", "100/minute")
    AUTH_RATE_LIMIT = os.getenv("AUTH_RATE_LIMIT", "5/minute")
    BACKTEST_RATE_LIMIT = os.getenv("BACKTEST_RATE_LIMIT", "10/minute")


class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_string(value: str, max_length: int = SecurityConfig.MAX_STRING_LENGTH) -> str:
        """Validate and sanitize string input"""
        if not isinstance(value, str):
            raise ValidationError("Input must be a string")
        
        if len(value) > max_length:
            raise ValidationError(f"String too long (max {max_length} characters)")
        
        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', value)
        
        return sanitized.strip()
    
    @staticmethod
    def validate_email(email: str) -> str:
        """Validate email format"""
        email = InputValidator.validate_string(email, 255)
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Invalid email format")
        
        return email.lower()
    
    @staticmethod
    def validate_username(username: str) -> str:
        """Validate username format"""
        username = InputValidator.validate_string(username, 50)
        
        if len(username) < 3:
            raise ValidationError("Username must be at least 3 characters long")
        
        # Allow alphanumeric, underscore, and hyphen
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            raise ValidationError("Username can only contain letters, numbers, underscore, and hyphen")
        
        return username
    
    @staticmethod
    def validate_symbol(symbol: str) -> str:
        """Validate trading symbol"""
        symbol = InputValidator.validate_string(symbol, 20)
        
        # Allow alphanumeric and dots (for some symbols like BRK.A)
        if not re.match(r'^[A-Z0-9.]+$', symbol.upper()):
            raise ValidationError("Invalid symbol format")
        
        return symbol.upper()
    
    @staticmethod
    def validate_list(value: List[Any], max_length: int = SecurityConfig.MAX_LIST_LENGTH) -> List[Any]:
        """Validate list input"""
        if not isinstance(value, list):
            raise ValidationError("Input must be a list")
        
        if len(value) > max_length:
            raise ValidationError(f"List too long (max {max_length} items)")
        
        return value


class FileValidator:
    """File upload validation utilities"""
    
    @staticmethod
    def validate_file_size(file: UploadFile) -> None:
        """Validate file size"""
        if file.size and file.size > SecurityConfig.MAX_FILE_SIZE:
            raise ValidationError(
                f"File too large (max {SecurityConfig.MAX_FILE_SIZE / 1024 / 1024:.1f}MB)"
            )
    
    @staticmethod
    def validate_file_extension(filename: str) -> None:
        """Validate file extension"""
        if not filename:
            raise ValidationError("Filename is required")
        
        file_ext = Path(filename).suffix.lower()
        if file_ext not in SecurityConfig.ALLOWED_EXTENSIONS:
            raise ValidationError(
                f"File type not allowed. Allowed types: {', '.join(SecurityConfig.ALLOWED_EXTENSIONS)}"
            )
    
    @staticmethod
    async def validate_file_content(file: UploadFile) -> None:
        """Validate file content using magic numbers"""
        # Read first 1024 bytes for magic number detection
        content = await file.read(1024)
        await file.seek(0)  # Reset file pointer
        
        # Detect MIME type
        mime_type = magic.from_buffer(content, mime=True)
        
        if mime_type not in SecurityConfig.ALLOWED_MIME_TYPES:
            raise ValidationError(f"File content type not allowed: {mime_type}")
    
    @staticmethod
    def generate_safe_filename(filename: str) -> str:
        """Generate a safe filename"""
        # Remove path components
        filename = Path(filename).name
        
        # Remove or replace dangerous characters
        safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # Ensure it doesn't start with a dot
        if safe_filename.startswith('.'):
            safe_filename = 'file' + safe_filename
        
        # Add timestamp to avoid conflicts
        import time
        timestamp = str(int(time.time()))
        name, ext = os.path.splitext(safe_filename)
        
        return f"{name}_{timestamp}{ext}"
    
    @staticmethod
    async def scan_file_for_malware(file_path: str) -> bool:
        """Basic malware scanning (placeholder for production implementation)"""
        # In production, integrate with ClamAV or similar
        # For now, just check for suspicious patterns
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read(1024 * 1024)  # Read first 1MB
            
            # Check for suspicious patterns
            suspicious_patterns = [
                b'eval(',
                b'exec(',
                b'__import__',
                b'subprocess',
                b'os.system',
                b'shell=True'
            ]
            
            for pattern in suspicious_patterns:
                if pattern in content:
                    logger.warning(f"Suspicious pattern found in file: {file_path}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {e}")
            return False


class SecurityMiddleware:
    """Security middleware for request validation"""
    
    def __init__(self, app):
        self.app = app
        self.logger = get_logger("quantdojo.security.middleware")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # Security headers
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                headers = dict(message.get("headers", []))
                
                # Add security headers
                security_headers = {
                    b"x-content-type-options": b"nosniff",
                    b"x-frame-options": b"DENY",
                    b"x-xss-protection": b"1; mode=block",
                    b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                    b"referrer-policy": b"strict-origin-when-cross-origin",
                    b"permissions-policy": b"geolocation=(), microphone=(), camera=()"
                }
                
                for header, value in security_headers.items():
                    if header not in headers:
                        headers[header] = value
                
                message["headers"] = list(headers.items())
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)


# Rate limiting decorators
def rate_limit(limit: str):
    """Rate limiting decorator"""
    return limiter.limit(limit)


# Common rate limits
auth_rate_limit = rate_limit(SecurityConfig.AUTH_RATE_LIMIT)
backtest_rate_limit = rate_limit(SecurityConfig.BACKTEST_RATE_LIMIT)
default_rate_limit = rate_limit(SecurityConfig.DEFAULT_RATE_LIMIT)
