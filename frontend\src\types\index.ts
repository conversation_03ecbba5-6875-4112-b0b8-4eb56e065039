// Core data types
export interface OHLCVData {
  timestamp: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MarketDataResponse {
  symbol: string;
  data: OHLCVData[];
  metadata: {
    symbol: string;
    company_name: string;
    sector?: string;
    industry?: string;
    currency: string;
    exchange: string;
    period: string;
    interval: string;
    data_points: number;
  };
}

// Strategy types
export type StrategyType = 'moving_average' | 'rsi';

export interface MovingAverageParams {
  fast_period: number;
  slow_period: number;
  ma_type: 'sma' | 'ema';
}

export interface RSIParams {
  period: number;
  overbought: number;
  oversold: number;
}

export type StrategyParams = MovingAverageParams | RSIParams;

export interface BacktestRequest {
  symbol: string;
  strategy: StrategyType;
  parameters: StrategyParams;
  start_date?: string;
  end_date?: string;
  initial_capital: number;
  commission: number;
  slippage: number;
}

// Trade and performance types
export interface Trade {
  entry_date: string;
  exit_date?: string;
  entry_price: number;
  exit_price?: number;
  quantity: number;
  side: 'buy' | 'sell';
  pnl?: number;
  pnl_pct?: number;
}

export interface PerformanceMetrics {
  total_return: number;
  total_return_pct: number;
  sharpe_ratio: number;
  max_drawdown: number;
  max_drawdown_pct: number;
  win_rate: number;
  profit_factor: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  avg_trade: number;
  avg_win: number;
  avg_loss: number;
  largest_win: number;
  largest_loss: number;
}

export interface EquityPoint {
  timestamp: string;
  equity: number;
  cash: number;
  position_value: number;
}

export interface IndicatorPoint {
  timestamp: string;
  value: number | null;
  name: string;
}

export interface SignalPoint {
  timestamp: string;
  value: number;
  type: 'buy' | 'sell';
  name: string;
}

export interface BacktestResult {
  symbol: string;
  strategy: string;
  parameters: Record<string, any>;
  start_date: string;
  end_date: string;
  initial_capital: number;
  final_capital: number;
  performance_metrics: PerformanceMetrics;
  trades: Trade[];
  equity_curve: EquityPoint[];
  indicators: {
    [key: string]: IndicatorPoint[] | SignalPoint[];
  };
}

// Strategy info types
export interface ParameterSchema {
  type: 'integer' | 'float' | 'select';
  default: any;
  min?: number;
  max?: number;
  step?: number;
  options?: string[];
  description: string;
}

export interface StrategyInfo {
  name: string;
  display_name: string;
  description: string;
  parameters: Record<string, ParameterSchema>;
  indicators: string[];
}

// UI state types
export interface BacktestState {
  // Data
  marketData: MarketDataResponse | null;
  backtestResult: BacktestResult | null;
  strategies: StrategyInfo[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Current configuration
  symbol: string;
  selectedStrategy: StrategyType;
  strategyParams: StrategyParams;
  backtestConfig: {
    initial_capital: number;
    commission: number;
    slippage: number;
    start_date?: string;
    end_date?: string;
  };
  
  // Actions
  setSymbol: (symbol: string) => void;
  setSelectedStrategy: (strategy: StrategyType) => void;
  setStrategyParams: (params: StrategyParams) => void;
  setBacktestConfig: (config: Partial<BacktestState['backtestConfig']>) => void;
  fetchMarketData: (symbol: string, period?: string) => Promise<void>;
  runBacktest: () => Promise<void>;
  fetchStrategies: () => Promise<void>;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

// Chart types for TradingView integration
export interface ChartData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface LineData {
  time: number;
  value: number;
}

export interface MarkerData {
  time: number;
  position: 'belowBar' | 'aboveBar';
  color: string;
  shape: 'circle' | 'square' | 'arrowUp' | 'arrowDown';
  text?: string;
  size?: number;
}
