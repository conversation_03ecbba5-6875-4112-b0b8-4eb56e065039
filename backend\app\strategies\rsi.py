"""
RSI Oscillator Strategy
"""

import pandas as pd
from typing import Dict, Any, Tuple, List
from app.core.models import OHLCVData
from app.strategies.base import BaseStrategy

class RSIStrategy(BaseStrategy):
    """
    RSI Oscillator Strategy
    
    Buy when RSI crosses below oversold level
    Sell when RSI crosses above overbought level
    """
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__(parameters)
        self.period = parameters.get('period', 14)
        self.overbought = parameters.get('overbought', 70.0)
        self.oversold = parameters.get('oversold', 30.0)
        
        # Validate parameters
        if self.oversold >= self.overbought:
            raise ValueError("Oversold level must be less than overbought level")
    
    def generate_signals(self, data: List[OHLCVData]) -> Tuple[pd.Series, Dict[str, List[Dict[str, Any]]]]:
        """
        Generate RSI-based trading signals
        """
        df = self._convert_data_to_df(data)
        
        # Calculate RSI
        df['rsi'] = self._calculate_rsi(df['close'], self.period)
        
        # Generate signals
        df['signal'] = 0
        df['prev_rsi'] = df['rsi'].shift(1)
        
        # Buy signal: RSI crosses below oversold (from above)
        buy_condition = (df['prev_rsi'] > self.oversold) & (df['rsi'] <= self.oversold)
        df.loc[buy_condition, 'signal'] = 1
        
        # Sell signal: RSI crosses above overbought (from below)
        sell_condition = (df['prev_rsi'] < self.overbought) & (df['rsi'] >= self.overbought)
        df.loc[sell_condition, 'signal'] = -1
        
        # Create signal series
        signals = df['signal'].copy()
        
        # Prepare indicator data for frontend
        indicators = {
            'rsi': self._format_indicator_data(df, 'rsi', 'RSI'),
            'overbought_line': [
                {
                    'timestamp': timestamp.isoformat(),
                    'value': self.overbought,
                    'name': f'Overbought ({self.overbought})'
                }
                for timestamp in df.index
            ],
            'oversold_line': [
                {
                    'timestamp': timestamp.isoformat(),
                    'value': self.oversold,
                    'name': f'Oversold ({self.oversold})'
                }
                for timestamp in df.index
            ],
            'signals': [
                {
                    'timestamp': timestamp.isoformat(),
                    'value': float(df.loc[timestamp, 'close']),
                    'type': 'buy' if signal == 1 else 'sell',
                    'name': 'Trade Signal'
                }
                for timestamp, signal in signals.items()
                if signal != 0
            ]
        }
        
        return signals, indicators
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for this strategy"""
        return {
            'period': {
                'type': 'integer',
                'default': 14,
                'min': 2,
                'max': 100,
                'description': 'RSI calculation period'
            },
            'overbought': {
                'type': 'float',
                'default': 70.0,
                'min': 50.0,
                'max': 100.0,
                'step': 1.0,
                'description': 'Overbought threshold'
            },
            'oversold': {
                'type': 'float',
                'default': 30.0,
                'min': 0.0,
                'max': 50.0,
                'step': 1.0,
                'description': 'Oversold threshold'
            }
        }
