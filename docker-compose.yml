version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: quantdojo-postgres
    environment:
      POSTGRES_DB: quantdojo
      POSTGRES_USER: quantdojo
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-quantdojo_dev_password}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quantdojo -d quantdojo"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - quantdojo-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: quantdojo-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_dev_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - quantdojo-network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      target: ${BUILD_TARGET:-development}
    container_name: quantdojo-backend
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD:-quantdojo_dev_password}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
      - SENTRY_DSN=${SENTRY_DSN:-}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - quantdojo-network
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      target: ${BUILD_TARGET:-development}
    container_name: quantdojo-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD:-quantdojo_dev_password}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
      - SENTRY_DSN=${SENTRY_DSN:-}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quantdojo-network
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./backend
      target: ${BUILD_TARGET:-development}
    container_name: quantdojo-celery-beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://quantdojo:${POSTGRES_PASSWORD:-quantdojo_dev_password}@postgres:5432/quantdojo
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_dev_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
      - SENTRY_DSN=${SENTRY_DSN:-}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quantdojo-network
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      target: ${BUILD_TARGET:-development}
    container_name: quantdojo-frontend
    environment:
      - VITE_API_URL=${VITE_API_URL:-http://localhost:8000}
      - VITE_ENVIRONMENT=${ENVIRONMENT:-development}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - quantdojo-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

networks:
  quantdojo-network:
    driver: bridge
