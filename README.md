# QuantDojo - Interactive Trading Strategy Backtester

## 🚀 Product Name Suggestions

1. **QuantDojo** - Master the art of quantitative trading
2. **BacktestLab** - Your trading strategy laboratory
3. **StrategyForge** - Forge winning trading strategies
4. **TradeSim Pro** - Professional trading simulation platform
5. **AlgoVault** - Secure your algorithmic trading edge

## 📋 Overview

QuantDojo is a comprehensive, production-ready web application for backtesting trading strategies. Built with modern technologies, it provides an intuitive interface for strategy development, testing, and performance analysis.

### Key Features

- 📊 **Interactive Strategy Configuration** - Real-time parameter adjustment with immediate backtesting
- 📈 **Advanced Charting** - TradingView Lightweight Charts with strategy overlays and trade markers
- 🔧 **Modular Strategy Framework** - Extensible architecture supporting multiple trading strategies
- 📉 **Comprehensive Analytics** - ROI, Sharpe ratio, max drawdown, win rate, and profit factor
- 📁 **Custom Data Support** - CSV file upload for proprietary datasets
- ⚡ **Real-time Performance** - Vectorized backtesting with Pandas/NumPy

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend (React + TypeScript)"
        A[Strategy Controls] --> B[Parameter Sliders]
        B --> C[TradingView Charts]
        C --> D[Performance Dashboard]
        D --> E[File Upload Interface]
    end
    
    subgraph "Backend (FastAPI)"
        F[Data API] --> G[yfinance Integration]
        H[Backtest API] --> I[Strategy Engine]
        J[Strategies API] --> K[Strategy Registry]
    end
    
    subgraph "Strategy Framework"
        L[Moving Average Crossover]
        M[RSI Oscillator]
        N[Custom Strategies]
    end
    
    A --> F
    A --> H
    A --> J
    I --> L
    I --> M
    I --> N
    
    subgraph "Data Sources"
        O[Yahoo Finance]
        P[CSV Upload]
    end
    
    G --> O
    E --> P
```

## 🔄 User Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant S as Strategy Engine
    participant D as Data Source
    
    U->>F: Select Strategy & Parameters
    F->>B: POST /api/backtest
    B->>D: Fetch OHLCV Data
    D-->>B: Return Market Data
    B->>S: Execute Strategy
    S-->>B: Return Results & Metrics
    B-->>F: JSON Response
    F->>F: Update Charts & Dashboard
    F-->>U: Display Results
    
    Note over U,F: Real-time parameter updates trigger automatic re-backtesting
```

## 📁 Project Structure

```
QuantDojo/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                 # FastAPI application entry point
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── data.py            # Data fetching endpoints
│   │   │   ├── backtest.py        # Backtesting endpoints
│   │   │   └── strategies.py      # Strategy management endpoints
│   │   ├── core/
│   │   │   ├── __init__.py
│   │   │   ├── config.py          # Application configuration
│   │   │   └── models.py          # Pydantic models
│   │   ├── strategies/
│   │   │   ├── __init__.py
│   │   │   ├── base.py            # Base strategy class
│   │   │   ├── moving_average.py  # MA Crossover strategy
│   │   │   └── rsi.py             # RSI strategy
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── backtester.py      # Core backtesting engine
│   │       ├── metrics.py         # Performance metrics calculation
│   │       └── data_loader.py     # Data loading utilities
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── components/
│   │   │   ├── Charts/
│   │   │   │   ├── TradingChart.tsx
│   │   │   │   └── EquityCurve.tsx
│   │   │   ├── Controls/
│   │   │   │   ├── StrategySelector.tsx
│   │   │   │   ├── ParameterControls.tsx
│   │   │   │   └── FileUpload.tsx
│   │   │   └── Dashboard/
│   │   │       ├── MetricsPanel.tsx
│   │   │       └── PerformanceTable.tsx
│   │   ├── hooks/
│   │   │   ├── useBacktest.ts
│   │   │   └── useMarketData.ts
│   │   ├── store/
│   │   │   └── backtestStore.ts    # Zustand state management
│   │   ├── types/
│   │   │   └── index.ts           # TypeScript type definitions
│   │   ├── utils/
│   │   │   └── api.ts             # API client utilities
│   │   ├── App.tsx
│   │   └── main.tsx
│   ├── package.json
│   ├── vite.config.ts
│   ├── tailwind.config.js
│   └── tsconfig.json
├── docker-compose.yml
├── .gitignore
└── README.md
```

## 🛠️ Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **Pandas & NumPy** - Vectorized data processing and backtesting
- **yfinance** - Market data fetching
- **Pydantic** - Data validation and serialization
- **Uvicorn** - ASGI server

### Frontend
- **React 18** - Modern UI library
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server
- **TradingView Lightweight Charts** - Professional charting library
- **Zustand** - Lightweight state management
- **Tailwind CSS** - Utility-first CSS framework

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/QuantDojo.git
cd QuantDojo
```

2. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

3. **Frontend Setup**
```bash
cd frontend
npm install
npm run dev
```

4. **Access the Application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 📊 Available Strategies

### 1. Moving Average Crossover
- **Parameters**: Fast MA period, Slow MA period, MA type (SMA/EMA)
- **Logic**: Buy when fast MA crosses above slow MA, sell when it crosses below
- **Indicators**: Displays both moving averages on the chart

### 2. RSI Oscillator
- **Parameters**: RSI period, overbought level, oversold level
- **Logic**: Buy when RSI crosses below oversold, sell when it crosses above overbought
- **Indicators**: RSI oscillator displayed in separate panel

## 📈 Performance Metrics

- **Return on Investment (ROI)** - Total percentage return
- **Sharpe Ratio** - Risk-adjusted return measure
- **Maximum Drawdown** - Largest peak-to-trough decline
- **Win Rate** - Percentage of profitable trades
- **Profit Factor** - Ratio of gross profit to gross loss
- **Total Trades** - Number of completed trades
- **Average Trade** - Mean profit/loss per trade

## 🔧 API Endpoints

### Data Endpoints
- `GET /api/data/{symbol}` - Fetch OHLCV data for a symbol
- `POST /api/data/upload` - Upload custom CSV data

### Backtesting Endpoints
- `POST /api/backtest` - Execute strategy backtest
- `GET /api/backtest/results/{id}` - Retrieve backtest results

### Strategy Endpoints
- `GET /api/strategies` - List available strategies
- `GET /api/strategies/{name}` - Get strategy details and parameters

## 🐳 Docker Deployment

```bash
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- TradingView for the excellent charting library
- Yahoo Finance for market data access
- The open-source community for the amazing tools and libraries
