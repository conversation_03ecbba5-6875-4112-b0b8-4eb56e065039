"""
Strategies API endpoints
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from app.core.models import StrategyInfo, StrategyListResponse, StrategyType

router = APIRouter()

# Strategy definitions
STRATEGIES = {
    "moving_average": StrategyInfo(
        name="moving_average",
        display_name="Moving Average Crossover",
        description="Buy when fast MA crosses above slow MA, sell when it crosses below",
        parameters={
            "fast_period": {
                "type": "integer",
                "default": 10,
                "min": 1,
                "max": 200,
                "description": "Fast moving average period"
            },
            "slow_period": {
                "type": "integer", 
                "default": 30,
                "min": 1,
                "max": 200,
                "description": "Slow moving average period"
            },
            "ma_type": {
                "type": "select",
                "default": "sma",
                "options": ["sma", "ema"],
                "description": "Moving average type"
            }
        },
        indicators=["fast_ma", "slow_ma"]
    ),
    "rsi": StrategyInfo(
        name="rsi",
        display_name="RSI Oscillator",
        description="Buy when RSI crosses below oversold, sell when it crosses above overbought",
        parameters={
            "period": {
                "type": "integer",
                "default": 14,
                "min": 2,
                "max": 100,
                "description": "RSI calculation period"
            },
            "overbought": {
                "type": "float",
                "default": 70.0,
                "min": 50.0,
                "max": 100.0,
                "description": "Overbought threshold"
            },
            "oversold": {
                "type": "float",
                "default": 30.0,
                "min": 0.0,
                "max": 50.0,
                "description": "Oversold threshold"
            }
        },
        indicators=["rsi", "overbought_line", "oversold_line"]
    )
}

@router.get("/")
async def list_strategies() -> StrategyListResponse:
    """
    Get list of all available strategies
    """
    return StrategyListResponse(strategies=list(STRATEGIES.values()))

@router.get("/{strategy_name}")
async def get_strategy_info(strategy_name: str) -> StrategyInfo:
    """
    Get detailed information about a specific strategy
    """
    if strategy_name not in STRATEGIES:
        raise HTTPException(
            status_code=404,
            detail=f"Strategy '{strategy_name}' not found"
        )
    
    return STRATEGIES[strategy_name]

@router.get("/{strategy_name}/parameters")
async def get_strategy_parameters(strategy_name: str) -> Dict[str, Any]:
    """
    Get parameter definitions for a specific strategy
    """
    if strategy_name not in STRATEGIES:
        raise HTTPException(
            status_code=404,
            detail=f"Strategy '{strategy_name}' not found"
        )
    
    return STRATEGIES[strategy_name].parameters
