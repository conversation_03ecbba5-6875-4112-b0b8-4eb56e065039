import React, { useEffect, useRef } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, LineData, Time } from 'lightweight-charts';
import { useBacktestStore } from '../../store/backtestStore';

export const EquityCurve: React.FC = () => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const equitySeriesRef = useRef<ISeriesApi<'Line'> | null>(null);

  const { backtestResult, isLoading } = useBacktestStore();

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#cccccc',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    // Create equity line series
    const equitySeries = chart.addLineSeries({
      color: '#3b82f6',
      lineWidth: 2,
      title: 'Portfolio Value',
      priceFormat: {
        type: 'price',
        precision: 2,
        minMove: 0.01,
      },
    });

    chartRef.current = chart;
    equitySeriesRef.current = equitySeries;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
      }
    };
  }, []);

  // Update equity curve data
  useEffect(() => {
    if (!chartRef.current || !equitySeriesRef.current || !backtestResult) return;

    if (backtestResult.equity_curve && backtestResult.equity_curve.length > 0) {
      // Prepare equity curve data
      const equityData: LineData[] = backtestResult.equity_curve.map(point => ({
        time: (new Date(point.timestamp).getTime() / 1000) as Time,
        value: point.equity,
      }));

      equitySeriesRef.current.setData(equityData);

      // Fit content to show all data
      chartRef.current.timeScale().fitContent();

      // Add horizontal line for initial capital
      const initialCapitalSeries = chartRef.current.addLineSeries({
        color: '#6b7280',
        lineWidth: 1,
        lineStyle: 2, // Dashed line
        title: 'Initial Capital',
        priceLineVisible: false,
      });

      const initialCapitalData: LineData[] = backtestResult.equity_curve.map(point => ({
        time: (new Date(point.timestamp).getTime() / 1000) as Time,
        value: backtestResult.initial_capital,
      }));

      initialCapitalSeries.setData(initialCapitalData);
    }
  }, [backtestResult]);

  if (isLoading) {
    return (
      <div className="chart-container">
        <div className="chart-loading">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading equity curve...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!backtestResult || !backtestResult.equity_curve || backtestResult.equity_curve.length === 0) {
    return (
      <div className="chart-container">
        <div className="chart-loading">
          <div className="text-center">
            <p className="text-gray-600">No backtest results</p>
            <p className="text-sm text-gray-500 mt-1">Run a backtest to see the equity curve</p>
          </div>
        </div>
      </div>
    );
  }

  const finalEquity = backtestResult.equity_curve[backtestResult.equity_curve.length - 1]?.equity || 0;
  const initialCapital = backtestResult.initial_capital;
  const totalReturn = finalEquity - initialCapital;
  const totalReturnPct = ((totalReturn / initialCapital) * 100);
  const isPositive = totalReturn >= 0;

  return (
    <div className="chart-container">
      <div ref={chartContainerRef} className="w-full h-full" />
      
      {/* Equity Summary */}
      <div className="absolute top-2 left-2 bg-white bg-opacity-90 rounded p-3 text-sm">
        <div className="space-y-1">
          <div className="flex justify-between items-center space-x-4">
            <span className="text-gray-600">Initial Capital:</span>
            <span className="font-medium">${initialCapital.toLocaleString()}</span>
          </div>
          <div className="flex justify-between items-center space-x-4">
            <span className="text-gray-600">Final Value:</span>
            <span className="font-medium">${finalEquity.toLocaleString()}</span>
          </div>
          <div className="flex justify-between items-center space-x-4">
            <span className="text-gray-600">Total Return:</span>
            <span className={`font-medium ${isPositive ? 'text-success-600' : 'text-danger-600'}`}>
              ${totalReturn.toLocaleString()} ({totalReturnPct.toFixed(2)}%)
            </span>
          </div>
        </div>
      </div>

      {/* Chart Legend */}
      <div className="absolute top-2 right-2 bg-white bg-opacity-90 rounded p-2 text-xs space-y-1">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-0.5 bg-primary-600" />
          <span className="text-gray-700">Portfolio Value</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-0.5 bg-gray-400 border-dashed border-t" />
          <span className="text-gray-700">Initial Capital</span>
        </div>
      </div>
    </div>
  );
};
